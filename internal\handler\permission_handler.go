package handler

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/limitcool/starter/internal/api/response"
	"github.com/limitcool/starter/internal/dto"
	"github.com/limitcool/starter/internal/middleware"
	"github.com/limitcool/starter/internal/model"
	"github.com/limitcool/starter/internal/pkg/errorx"
	"github.com/limitcool/starter/internal/pkg/logger"
)

// PermissionHandler 权限处理器
type PermissionHandler struct {
	permissionRepo *model.PermissionRepo
}

// NewPermissionHandler 创建权限处理器
func NewPermissionHandler(permissionRepo *model.PermissionRepo) *PermissionHandler {
	return &PermissionHandler{
		permissionRepo: permissionRepo,
	}
}

// CreatePermission 创建权限
// @Summary 创建权限
// @Description 创建新的权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param request body dto.CreatePermissionRequest true "创建权限请求"
// @Success 200 {object} response.Response{data=dto.PermissionResponse} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/admin/permissions [post]
func (h *PermissionHandler) CreatePermission(c *gin.Context) {
	ctx := c.Request.Context()
	var req dto.CreatePermissionRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.WarnContext(ctx, "创建权限参数绑定失败", "error", err)
		response.Error(c, errorx.ErrInvalidParams.WithMsg("请求参数错误"))
		return
	}

	// 检查权限名是否已存在
	existingPermission, err := h.permissionRepo.List(ctx, 1, 1, &model.QueryOptions{
		Conditions: map[string]any{
			"name": req.Name,
		},
	})
	if err != nil {
		logger.ErrorContext(ctx, "检查权限名失败", "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("检查权限名失败"))
		return
	}
	if len(existingPermission) > 0 {
		response.Error(c, errorx.ErrInvalidParams.WithMsg("权限名已存在"))
		return
	}

	// 检查资源和操作组合是否已存在
	exists, err := h.permissionRepo.CheckPermission(ctx, req.Resource, req.Action)
	if err != nil {
		logger.ErrorContext(ctx, "检查权限组合失败", "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("检查权限组合失败"))
		return
	}
	if exists {
		response.Error(c, errorx.ErrInvalidParams.WithMsg("该资源和操作的权限已存在"))
		return
	}

	// 创建权限
	permission := &model.Permission{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
		Resource:    req.Resource,
		Action:      req.Action,
		Method:      req.Method,
		Path:        req.Path,
		Category:    req.Category,
		Enabled:     true,
		Sort:        0,
		CreatedBy:   middleware.GetUserIDInt64(c),
		UpdatedBy:   middleware.GetUserIDInt64(c),
	}

	if req.Enabled != nil {
		permission.Enabled = *req.Enabled
	}
	if req.Sort != nil {
		permission.Sort = *req.Sort
	}

	err = h.permissionRepo.Create(ctx, permission)
	if err != nil {
		logger.ErrorContext(ctx, "创建权限失败", "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("创建权限失败"))
		return
	}

	// 转换为响应格式
	permResp := &dto.PermissionResponse{
		ID:          permission.ID,
		Name:        permission.Name,
		DisplayName: permission.DisplayName,
		Description: permission.Description,
		Resource:    permission.Resource,
		Action:      permission.Action,
		Method:      permission.Method,
		Path:        permission.Path,
		Category:    permission.Category,
		Enabled:     permission.Enabled,
		IsSystem:    permission.IsSystem,
		Sort:        permission.Sort,
		CreatedBy:   permission.CreatedBy,
		UpdatedBy:   permission.UpdatedBy,
		CreatedAt:   permission.CreatedAt,
		UpdatedAt:   permission.UpdatedAt,
	}

	logger.InfoContext(ctx, "创建权限成功", "permission_id", permission.ID, "permission_name", permission.Name)
	response.Success(c, permResp)
}

// UpdatePermission 更新权限
// @Summary 更新权限
// @Description 更新权限信息
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param id path int true "权限ID"
// @Param request body dto.UpdatePermissionRequest true "更新权限请求"
// @Success 200 {object} response.Response{data=dto.PermissionResponse} "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "权限不存在"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/admin/permissions/{id} [put]
func (h *PermissionHandler) UpdatePermission(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取权限ID
	permissionID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.Error(c, errorx.ErrInvalidParams.WithMsg("无效的权限ID"))
		return
	}

	var req dto.UpdatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.WarnContext(ctx, "更新权限参数绑定失败", "error", err)
		response.Error(c, errorx.ErrInvalidParams.WithMsg("请求参数错误"))
		return
	}

	// 获取现有权限
	permission, err := h.permissionRepo.Get(ctx, permissionID, nil)
	if err != nil {
		logger.ErrorContext(ctx, "获取权限失败", "permission_id", permissionID, "error", err)
		response.Error(c, errorx.ErrPermissionNotFound)
		return
	}

	// 检查系统权限
	if permission.IsSystem {
		response.Error(c, errorx.ErrForbidden.WithMsg("系统权限不允许修改"))
		return
	}

	// 检查权限名是否已被其他权限使用
	if req.Name != permission.Name {
		existingPermission, err := h.permissionRepo.List(ctx, 1, 1, &model.QueryOptions{
			Conditions: map[string]any{
				"name": req.Name,
			},
		})
		if err != nil {
			logger.ErrorContext(ctx, "检查权限名失败", "error", err)
			response.Error(c, errorx.ErrInternal.WithMsg("检查权限名失败"))
			return
		}
		if len(existingPermission) > 0 && existingPermission[0].ID != permissionID {
			response.Error(c, errorx.ErrInvalidParams.WithMsg("权限名已存在"))
			return
		}
	}

	// 检查资源和操作组合是否已被其他权限使用
	if req.Resource != permission.Resource || req.Action != permission.Action {
		existingPerm, err := h.permissionRepo.GetPermissionByResourceAction(ctx, req.Resource, req.Action)
		if err == nil && existingPerm.ID != permissionID {
			response.Error(c, errorx.ErrInvalidParams.WithMsg("该资源和操作的权限已存在"))
			return
		}
	}

	// 更新权限信息
	permission.Name = req.Name
	permission.DisplayName = req.DisplayName
	permission.Description = req.Description
	permission.Resource = req.Resource
	permission.Action = req.Action
	permission.Method = req.Method
	permission.Path = req.Path
	permission.Category = req.Category
	permission.UpdatedBy = middleware.GetUserIDInt64(c)

	if req.Enabled != nil {
		permission.Enabled = *req.Enabled
	}
	if req.Sort != nil {
		permission.Sort = *req.Sort
	}

	err = h.permissionRepo.Update(ctx, permission)
	if err != nil {
		logger.ErrorContext(ctx, "更新权限失败", "permission_id", permissionID, "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("更新权限失败"))
		return
	}

	// 转换为响应格式
	permResp := &dto.PermissionResponse{
		ID:          permission.ID,
		Name:        permission.Name,
		DisplayName: permission.DisplayName,
		Description: permission.Description,
		Resource:    permission.Resource,
		Action:      permission.Action,
		Method:      permission.Method,
		Path:        permission.Path,
		Category:    permission.Category,
		Enabled:     permission.Enabled,
		IsSystem:    permission.IsSystem,
		Sort:        permission.Sort,
		CreatedBy:   permission.CreatedBy,
		UpdatedBy:   permission.UpdatedBy,
		CreatedAt:   permission.CreatedAt,
		UpdatedAt:   permission.UpdatedAt,
	}

	logger.InfoContext(ctx, "更新权限成功", "permission_id", permissionID, "permission_name", permission.Name)
	response.Success(c, permResp)
}

// DeletePermission 删除权限
// @Summary 删除权限
// @Description 删除权限
// @Tags 权限管理
// @Produce json
// @Param id path int true "权限ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "权限不存在"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/admin/permissions/{id} [delete]
func (h *PermissionHandler) DeletePermission(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取权限ID
	permissionID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.Error(c, errorx.ErrInvalidParams.WithMsg("无效的权限ID"))
		return
	}

	// 获取权限信息
	permission, err := h.permissionRepo.Get(ctx, permissionID, nil)
	if err != nil {
		logger.ErrorContext(ctx, "获取权限失败", "permission_id", permissionID, "error", err)
		response.Error(c, errorx.ErrPermissionNotFound)
		return
	}

	// 检查系统权限
	if permission.IsSystem {
		response.Error(c, errorx.ErrForbidden.WithMsg("系统权限不允许删除"))
		return
	}

	// 删除权限
	err = h.permissionRepo.Delete(ctx, permissionID)
	if err != nil {
		logger.ErrorContext(ctx, "删除权限失败", "permission_id", permissionID, "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("删除权限失败"))
		return
	}

	logger.InfoContext(ctx, "删除权限成功", "permission_id", permissionID, "permission_name", permission.Name)
	response.Success[any](c, nil)
}

// GetPermission 获取权限详情
// @Summary 获取权限详情
// @Description 获取权限详细信息
// @Tags 权限管理
// @Produce json
// @Param id path int true "权限ID"
// @Success 200 {object} response.Response{data=dto.PermissionResponse} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "权限不存在"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/admin/permissions/{id} [get]
func (h *PermissionHandler) GetPermission(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取权限ID
	permissionID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.Error(c, errorx.ErrInvalidParams.WithMsg("无效的权限ID"))
		return
	}

	// 获取权限信息
	permission, err := h.permissionRepo.Get(ctx, permissionID, nil)
	if err != nil {
		logger.ErrorContext(ctx, "获取权限失败", "permission_id", permissionID, "error", err)
		response.Error(c, errorx.ErrPermissionNotFound)
		return
	}

	// 转换为响应格式
	permResp := &dto.PermissionResponse{
		ID:          permission.ID,
		Name:        permission.Name,
		DisplayName: permission.DisplayName,
		Description: permission.Description,
		Resource:    permission.Resource,
		Action:      permission.Action,
		Method:      permission.Method,
		Path:        permission.Path,
		Category:    permission.Category,
		Enabled:     permission.Enabled,
		IsSystem:    permission.IsSystem,
		Sort:        permission.Sort,
		CreatedBy:   permission.CreatedBy,
		UpdatedBy:   permission.UpdatedBy,
		CreatedAt:   permission.CreatedAt,
		UpdatedAt:   permission.UpdatedAt,
	}

	response.Success(c, permResp)
}
