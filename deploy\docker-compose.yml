version: '3.8'

services:
  # 主应用服务
  starter:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
      args:
        VERSION: ${VERSION:-dev}
        GIT_COMMIT: ${GIT_COMMIT:-unknown}
        BUILD_DATE: ${BUILD_DATE:-unknown}
    image: starter:${VERSION:-latest}
    container_name: starter-app
    ports:
      - "8080:8080"
      - "6060:6060"  # pprof端口（如果启用）
    environment:
      - APP_MODE=release
      - APP_PORT=8080
    volumes:
      - ./config:/app/config:ro  # 配置文件目录
      - starter_logs:/app/logs   # 日志目录
    networks:
      - starter-network
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: starter-postgres
    environment:
      POSTGRES_DB: starter
      POSTGRES_USER: starter
      POSTGRES_PASSWORD: starter123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init:/docker-entrypoint-initdb.d:ro  # 初始化脚本
    ports:
      - "5432:5432"
    networks:
      - starter-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U starter"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: starter-redis
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - starter-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # MinIO 对象存储（可选）
  minio:
    image: minio/minio:latest
    container_name: starter-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"  # API端口
      - "9001:9001"  # 控制台端口
    networks:
      - starter-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

# 网络配置
networks:
  starter-network:
    driver: bridge

# 数据卷配置
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  starter_logs:
    driver: local
