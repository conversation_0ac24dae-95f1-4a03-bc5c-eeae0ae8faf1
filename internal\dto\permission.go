package dto

import "time"

// CreatePermissionRequest 创建权限请求
type CreatePermissionRequest struct {
	Name        string `json:"name" binding:"required,max=100" example:"user:read"`              // 权限名称
	DisplayName string `json:"display_name" binding:"required,max=100" example:"用户查看"`         // 权限显示名称
	Description string `json:"description" binding:"max=500" example:"查看用户信息的权限"`              // 权限描述
	Resource    string `json:"resource" binding:"required,max=100" example:"user"`               // 资源标识
	Action      string `json:"action" binding:"required,max=50" example:"read"`                  // 操作类型
	Method      string `json:"method" binding:"max=10" example:"GET"`                            // HTTP方法
	Path        string `json:"path" binding:"max=255" example:"/api/v1/users"`                   // API路径
	Category    string `json:"category" binding:"max=50" example:"用户管理"`                        // 权限分类
	Enabled     *bool  `json:"enabled" example:"true"`                                           // 是否启用
	Sort        *int   `json:"sort" example:"1"`                                                 // 排序
}

// UpdatePermissionRequest 更新权限请求
type UpdatePermissionRequest struct {
	Name        string `json:"name" binding:"required,max=100" example:"user:read"`              // 权限名称
	DisplayName string `json:"display_name" binding:"required,max=100" example:"用户查看"`         // 权限显示名称
	Description string `json:"description" binding:"max=500" example:"查看用户信息的权限"`              // 权限描述
	Resource    string `json:"resource" binding:"required,max=100" example:"user"`               // 资源标识
	Action      string `json:"action" binding:"required,max=50" example:"read"`                  // 操作类型
	Method      string `json:"method" binding:"max=10" example:"GET"`                            // HTTP方法
	Path        string `json:"path" binding:"max=255" example:"/api/v1/users"`                   // API路径
	Category    string `json:"category" binding:"max=50" example:"用户管理"`                        // 权限分类
	Enabled     *bool  `json:"enabled" example:"true"`                                           // 是否启用
	Sort        *int   `json:"sort" example:"1"`                                                 // 排序
}

// PermissionListRequest 权限列表请求
type PermissionListRequest struct {
	Page     int    `form:"page" binding:"min=1" example:"1"`                    // 页码
	PageSize int    `form:"page_size" binding:"min=1,max=100" example:"10"`      // 每页数量
	Keyword  string `form:"keyword" example:"user"`                              // 关键字搜索
	Category string `form:"category" example:"用户管理"`                            // 权限分类
	Resource string `form:"resource" example:"user"`                             // 资源标识
	Enabled  *bool  `form:"enabled" example:"true"`                              // 是否启用
}

// PermissionResponse 权限响应
type PermissionResponse struct {
	ID          int64     `json:"id" example:"1"`                                      // 权限ID
	Name        string    `json:"name" example:"user:read"`                            // 权限名称
	DisplayName string    `json:"display_name" example:"用户查看"`                       // 权限显示名称
	Description string    `json:"description" example:"查看用户信息的权限"`                  // 权限描述
	Resource    string    `json:"resource" example:"user"`                             // 资源标识
	Action      string    `json:"action" example:"read"`                               // 操作类型
	Method      string    `json:"method" example:"GET"`                                // HTTP方法
	Path        string    `json:"path" example:"/api/v1/users"`                       // API路径
	Category    string    `json:"category" example:"用户管理"`                            // 权限分类
	Enabled     bool      `json:"enabled" example:"true"`                              // 是否启用
	IsSystem    bool      `json:"is_system" example:"false"`                           // 是否系统权限
	Sort        int       `json:"sort" example:"1"`                                    // 排序
	CreatedBy   int64     `json:"created_by" example:"1"`                              // 创建者ID
	UpdatedBy   int64     `json:"updated_by" example:"1"`                              // 更新者ID
	CreatedAt   time.Time `json:"created_at" example:"2023-01-01T00:00:00Z"`           // 创建时间
	UpdatedAt   time.Time `json:"updated_at" example:"2023-01-01T00:00:00Z"`           // 更新时间
}

// PermissionListResponse 权限列表响应
type PermissionListResponse struct {
	List  []PermissionResponse `json:"list"`  // 权限列表
	Total int64                `json:"total"` // 总数
}

// PermissionSimpleResponse 权限简单响应
type PermissionSimpleResponse struct {
	ID          int64  `json:"id" example:"1"`                  // 权限ID
	Name        string `json:"name" example:"user:read"`        // 权限名称
	DisplayName string `json:"display_name" example:"用户查看"`   // 权限显示名称
	Resource    string `json:"resource" example:"user"`         // 资源标识
	Action      string `json:"action" example:"read"`           // 操作类型
	Category    string `json:"category" example:"用户管理"`        // 权限分类
	Enabled     bool   `json:"enabled" example:"true"`          // 是否启用
}

// PermissionCategoryResponse 权限分类响应
type PermissionCategoryResponse struct {
	Category    string                     `json:"category"`    // 分类名称
	Permissions []PermissionSimpleResponse `json:"permissions"` // 分类下的权限列表
}
