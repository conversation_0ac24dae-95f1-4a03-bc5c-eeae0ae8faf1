package handler

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/limitcool/starter/internal/api/response"
	"github.com/limitcool/starter/internal/dto"
	"github.com/limitcool/starter/internal/middleware"
	"github.com/limitcool/starter/internal/model"
	"github.com/limitcool/starter/internal/pkg/errorx"
	"github.com/limitcool/starter/internal/pkg/logger"
	"github.com/limitcool/starter/internal/pkg/permission"
)

// RoleHandler 角色处理器
type RoleHandler struct {
	roleRepo          *model.RoleRepo
	permissionService *permission.Service
}

// NewRoleHandler 创建角色处理器
func NewRoleHandler(roleRepo *model.RoleRepo, permissionService *permission.Service) *RoleHandler {
	return &RoleHandler{
		roleRepo:          roleRepo,
		permissionService: permissionService,
	}
}

// CreateRole 创建角色
// @Summary 创建角色
// @Description 创建新的角色
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param request body dto.CreateRoleRequest true "创建角色请求"
// @Success 200 {object} response.Response{data=dto.RoleResponse} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/admin/roles [post]
func (h *RoleHandler) CreateRole(c *gin.Context) {
	ctx := c.Request.Context()
	var req dto.CreateRoleRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.WarnContext(ctx, "创建角色参数绑定失败", "error", err)
		response.Error(c, errorx.ErrInvalidParams.WithMsg("请求参数错误"))
		return
	}

	// 检查角色名是否已存在
	existingRole, err := h.roleRepo.List(ctx, 1, 1, &model.QueryOptions{
		Conditions: map[string]any{
			"name": req.Name,
		},
	})
	if err != nil {
		logger.ErrorContext(ctx, "检查角色名失败", "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("检查角色名失败"))
		return
	}
	if len(existingRole) > 0 {
		response.Error(c, errorx.ErrInvalidParams.WithMsg("角色名已存在"))
		return
	}

	// 创建角色
	role := &model.Role{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
		Enabled:     true,
		Sort:        0,
		CreatedBy:   middleware.GetUserIDInt64(c),
		UpdatedBy:   middleware.GetUserIDInt64(c),
	}

	if req.Enabled != nil {
		role.Enabled = *req.Enabled
	}
	if req.Sort != nil {
		role.Sort = *req.Sort
	}

	err = h.roleRepo.Create(ctx, role)
	if err != nil {
		logger.ErrorContext(ctx, "创建角色失败", "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("创建角色失败"))
		return
	}

	// 转换为响应格式
	roleResp := &dto.RoleResponse{
		ID:          role.ID,
		Name:        role.Name,
		DisplayName: role.DisplayName,
		Description: role.Description,
		Enabled:     role.Enabled,
		IsSystem:    role.IsSystem,
		Sort:        role.Sort,
		CreatedBy:   role.CreatedBy,
		UpdatedBy:   role.UpdatedBy,
		CreatedAt:   role.CreatedAt,
		UpdatedAt:   role.UpdatedAt,
	}

	logger.InfoContext(ctx, "创建角色成功", "role_id", role.ID, "role_name", role.Name)
	response.Success(c, roleResp)
}

// UpdateRole 更新角色
// @Summary 更新角色
// @Description 更新角色信息
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param id path int true "角色ID"
// @Param request body dto.UpdateRoleRequest true "更新角色请求"
// @Success 200 {object} response.Response{data=dto.RoleResponse} "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "角色不存在"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/admin/roles/{id} [put]
func (h *RoleHandler) UpdateRole(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取角色ID
	roleID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.Error(c, errorx.ErrInvalidParams.WithMsg("无效的角色ID"))
		return
	}

	var req dto.UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.WarnContext(ctx, "更新角色参数绑定失败", "error", err)
		response.Error(c, errorx.ErrInvalidParams.WithMsg("请求参数错误"))
		return
	}

	// 获取现有角色
	role, err := h.roleRepo.Get(ctx, roleID, nil)
	if err != nil {
		logger.ErrorContext(ctx, "获取角色失败", "role_id", roleID, "error", err)
		response.Error(c, errorx.ErrRoleNotFound)
		return
	}

	// 检查系统角色
	if role.IsSystem {
		response.Error(c, errorx.ErrForbidden.WithMsg("系统角色不允许修改"))
		return
	}

	// 检查角色名是否已被其他角色使用
	if req.Name != role.Name {
		existingRole, err := h.roleRepo.List(ctx, 1, 1, &model.QueryOptions{
			Conditions: map[string]any{
				"name": req.Name,
			},
		})
		if err != nil {
			logger.ErrorContext(ctx, "检查角色名失败", "error", err)
			response.Error(c, errorx.ErrInternal.WithMsg("检查角色名失败"))
			return
		}
		if len(existingRole) > 0 && existingRole[0].ID != roleID {
			response.Error(c, errorx.ErrInvalidParams.WithMsg("角色名已存在"))
			return
		}
	}

	// 更新角色信息
	role.Name = req.Name
	role.DisplayName = req.DisplayName
	role.Description = req.Description
	role.UpdatedBy = middleware.GetUserIDInt64(c)

	if req.Enabled != nil {
		role.Enabled = *req.Enabled
	}
	if req.Sort != nil {
		role.Sort = *req.Sort
	}

	err = h.roleRepo.Update(ctx, role)
	if err != nil {
		logger.ErrorContext(ctx, "更新角色失败", "role_id", roleID, "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("更新角色失败"))
		return
	}

	// 转换为响应格式
	roleResp := &dto.RoleResponse{
		ID:          role.ID,
		Name:        role.Name,
		DisplayName: role.DisplayName,
		Description: role.Description,
		Enabled:     role.Enabled,
		IsSystem:    role.IsSystem,
		Sort:        role.Sort,
		CreatedBy:   role.CreatedBy,
		UpdatedBy:   role.UpdatedBy,
		CreatedAt:   role.CreatedAt,
		UpdatedAt:   role.UpdatedAt,
	}

	logger.InfoContext(ctx, "更新角色成功", "role_id", roleID, "role_name", role.Name)
	response.Success(c, roleResp)
}

// DeleteRole 删除角色
// @Summary 删除角色
// @Description 删除角色
// @Tags 角色管理
// @Produce json
// @Param id path int true "角色ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "角色不存在"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/admin/roles/{id} [delete]
func (h *RoleHandler) DeleteRole(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取角色ID
	roleID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.Error(c, errorx.ErrInvalidParams.WithMsg("无效的角色ID"))
		return
	}

	// 获取角色信息
	role, err := h.roleRepo.Get(ctx, roleID, nil)
	if err != nil {
		logger.ErrorContext(ctx, "获取角色失败", "role_id", roleID, "error", err)
		response.Error(c, errorx.ErrRoleNotFound)
		return
	}

	// 检查系统角色
	if role.IsSystem {
		response.Error(c, errorx.ErrForbidden.WithMsg("系统角色不允许删除"))
		return
	}

	// 删除角色
	err = h.roleRepo.Delete(ctx, roleID)
	if err != nil {
		logger.ErrorContext(ctx, "删除角色失败", "role_id", roleID, "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("删除角色失败"))
		return
	}

	logger.InfoContext(ctx, "删除角色成功", "role_id", roleID, "role_name", role.Name)
	response.Success[any](c, nil)
}

// GetRole 获取角色详情
// @Summary 获取角色详情
// @Description 获取角色详细信息，包括权限和用户
// @Tags 角色管理
// @Produce json
// @Param id path int true "角色ID"
// @Success 200 {object} response.Response{data=dto.RoleDetailResponse} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "角色不存在"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/admin/roles/{id} [get]
func (h *RoleHandler) GetRole(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取角色ID
	roleID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.Error(c, errorx.ErrInvalidParams.WithMsg("无效的角色ID"))
		return
	}

	// 获取角色及其权限
	role, err := h.roleRepo.GetRoleWithPermissions(ctx, roleID)
	if err != nil {
		logger.ErrorContext(ctx, "获取角色详情失败", "role_id", roleID, "error", err)
		response.Error(c, errorx.ErrRoleNotFound)
		return
	}

	// 转换权限为响应格式
	var permissions []dto.PermissionResponse
	for _, perm := range role.Permissions {
		permissions = append(permissions, dto.PermissionResponse{
			ID:          perm.ID,
			Name:        perm.Name,
			DisplayName: perm.DisplayName,
			Description: perm.Description,
			Resource:    perm.Resource,
			Action:      perm.Action,
			Method:      perm.Method,
			Path:        perm.Path,
			Category:    perm.Category,
			Enabled:     perm.Enabled,
			IsSystem:    perm.IsSystem,
			Sort:        perm.Sort,
			CreatedBy:   perm.CreatedBy,
			UpdatedBy:   perm.UpdatedBy,
			CreatedAt:   perm.CreatedAt,
			UpdatedAt:   perm.UpdatedAt,
		})
	}

	// 转换用户为响应格式
	var users []dto.UserSimpleResponse
	for _, user := range role.Users {
		users = append(users, dto.UserSimpleResponse{
			ID:       user.ID,
			Username: user.Username,
			Nickname: user.Nickname,
			Avatar:   user.AvatarURL,
			Enabled:  user.Enabled,
		})
	}

	// 构建响应
	roleResp := &dto.RoleDetailResponse{
		RoleResponse: dto.RoleResponse{
			ID:          role.ID,
			Name:        role.Name,
			DisplayName: role.DisplayName,
			Description: role.Description,
			Enabled:     role.Enabled,
			IsSystem:    role.IsSystem,
			Sort:        role.Sort,
			CreatedBy:   role.CreatedBy,
			UpdatedBy:   role.UpdatedBy,
			CreatedAt:   role.CreatedAt,
			UpdatedAt:   role.UpdatedAt,
		},
		Permissions: permissions,
		Users:       users,
	}

	response.Success(c, roleResp)
}

// ListRoles 获取角色列表
// @Summary 获取角色列表
// @Description 获取角色列表，支持分页和搜索
// @Tags 角色管理
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "关键字搜索"
// @Param enabled query bool false "是否启用"
// @Success 200 {object} response.Response{data=dto.RoleListResponse} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/admin/roles [get]
func (h *RoleHandler) ListRoles(c *gin.Context) {
	ctx := c.Request.Context()
	var req dto.RoleListRequest

	if err := c.ShouldBindQuery(&req); err != nil {
		logger.WarnContext(ctx, "角色列表参数绑定失败", "error", err)
		response.Error(c, errorx.ErrInvalidParams.WithMsg("请求参数错误"))
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	opts := &model.QueryOptions{
		OrderBy: "sort ASC, created_at DESC",
	}

	conditions := make(map[string]any)
	if req.Enabled != nil {
		conditions["enabled"] = *req.Enabled
	}

	// 关键字搜索
	if req.Keyword != "" {
		opts.Condition = "name LIKE ? OR display_name LIKE ? OR description LIKE ?"
		opts.Args = []any{
			"%" + req.Keyword + "%",
			"%" + req.Keyword + "%",
			"%" + req.Keyword + "%",
		}
	}

	if len(conditions) > 0 {
		opts.Conditions = conditions
	}

	// 获取角色列表
	roles, err := h.roleRepo.List(ctx, req.Page, req.PageSize, opts)
	if err != nil {
		logger.ErrorContext(ctx, "获取角色列表失败", "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("获取角色列表失败"))
		return
	}

	// 获取总数
	total, err := h.roleRepo.Count(ctx, opts)
	if err != nil {
		logger.ErrorContext(ctx, "获取角色总数失败", "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("获取角色总数失败"))
		return
	}

	// 转换为响应格式
	var roleList []dto.RoleResponse
	for _, role := range roles {
		roleList = append(roleList, dto.RoleResponse{
			ID:          role.ID,
			Name:        role.Name,
			DisplayName: role.DisplayName,
			Description: role.Description,
			Enabled:     role.Enabled,
			IsSystem:    role.IsSystem,
			Sort:        role.Sort,
			CreatedBy:   role.CreatedBy,
			UpdatedBy:   role.UpdatedBy,
			CreatedAt:   role.CreatedAt,
			UpdatedAt:   role.UpdatedAt,
		})
	}

	resp := &dto.RoleListResponse{
		List:  roleList,
		Total: total,
	}

	response.Success(c, resp)
}

// AssignPermissions 为角色分配权限
// @Summary 为角色分配权限
// @Description 为角色分配权限
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param id path int true "角色ID"
// @Param request body dto.AssignPermissionsRequest true "分配权限请求"
// @Success 200 {object} response.Response "分配成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "角色不存在"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/admin/roles/{id}/permissions [post]
func (h *RoleHandler) AssignPermissions(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取角色ID
	roleID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.Error(c, errorx.ErrInvalidParams.WithMsg("无效的角色ID"))
		return
	}

	var req dto.AssignPermissionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.WarnContext(ctx, "分配权限参数绑定失败", "error", err)
		response.Error(c, errorx.ErrInvalidParams.WithMsg("请求参数错误"))
		return
	}

	// 检查角色是否存在
	role, err := h.roleRepo.Get(ctx, roleID, nil)
	if err != nil {
		logger.ErrorContext(ctx, "获取角色失败", "role_id", roleID, "error", err)
		response.Error(c, errorx.ErrRoleNotFound)
		return
	}

	// 检查系统角色
	if role.IsSystem {
		response.Error(c, errorx.ErrForbidden.WithMsg("系统角色不允许修改权限"))
		return
	}

	// 分配权限
	err = h.permissionService.AssignPermissionsToRole(ctx, roleID, req.PermissionIDs)
	if err != nil {
		logger.ErrorContext(ctx, "分配角色权限失败", "role_id", roleID, "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("分配权限失败"))
		return
	}

	logger.InfoContext(ctx, "分配角色权限成功",
		"role_id", roleID,
		"role_name", role.Name,
		"permission_count", len(req.PermissionIDs))
	response.Success[any](c, nil)
}
