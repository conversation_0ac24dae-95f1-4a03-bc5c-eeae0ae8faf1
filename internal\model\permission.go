package model

import (
	"context"
	"time"

	"github.com/limitcool/starter/internal/pkg/errorx"
	"gorm.io/gorm"
)

// Permission 权限模型
type Permission struct {
	SnowflakeModel

	Name        string     `json:"name" gorm:"size:100;not null;unique;comment:权限名称"`
	DisplayName string     `json:"display_name" gorm:"size:100;not null;comment:权限显示名称"`
	Description string     `json:"description" gorm:"size:500;comment:权限描述"`
	Resource    string     `json:"resource" gorm:"size:100;not null;comment:资源标识"`
	Action      string     `json:"action" gorm:"size:50;not null;comment:操作类型"`
	Method      string     `json:"method" gorm:"size:10;comment:HTTP方法"`
	Path        string     `json:"path" gorm:"size:255;comment:API路径"`
	Category    string     `json:"category" gorm:"size:50;comment:权限分类"`
	Enabled     bool       `json:"enabled" gorm:"default:true;comment:是否启用"`
	IsSystem    bool       `json:"is_system" gorm:"default:false;comment:是否系统权限"`
	Sort        int        `json:"sort" gorm:"default:0;comment:排序"`
	CreatedBy   int64      `json:"created_by" gorm:"comment:创建者ID"`
	UpdatedBy   int64      `json:"updated_by" gorm:"comment:更新者ID"`
	DeletedAt   *time.Time `json:"deleted_at" gorm:"index;comment:删除时间"`

	// 关联关系
	Roles []Role `json:"roles,omitempty" gorm:"many2many:role_permissions;"`
	Menus []Menu `json:"menus,omitempty" gorm:"many2many:menu_permissions;"`
}

func (Permission) TableName() string {
	return "permission"
}

func NewPermission() *Permission {
	return &Permission{}
}

// PermissionRepo 权限仓库
type PermissionRepo struct {
	*GenericRepo[Permission]
}

// NewPermissionRepo 创建权限仓库
func NewPermissionRepo(db *gorm.DB) *PermissionRepo {
	return &PermissionRepo{
		GenericRepo: NewGenericRepo[Permission](db),
	}
}

// GetPermissionsByRoleID 根据角色ID获取权限列表
func (r *PermissionRepo) GetPermissionsByRoleID(ctx context.Context, roleID int64) ([]Permission, error) {
	var permissions []Permission
	err := r.DB.WithContext(ctx).
		Table("permission").
		Joins("JOIN role_permissions ON permission.id = role_permissions.permission_id").
		Where("role_permissions.role_id = ? AND permission.enabled = ? AND permission.deleted_at IS NULL", roleID, true).
		Find(&permissions).Error

	if err != nil {
		return nil, errorx.WrapError(err, "查询角色权限失败")
	}
	return permissions, nil
}

// GetPermissionsByUserID 根据用户ID获取权限列表
func (r *PermissionRepo) GetPermissionsByUserID(ctx context.Context, userID int64) ([]Permission, error) {
	var permissions []Permission
	err := r.DB.WithContext(ctx).
		Table("permission").
		Joins("JOIN role_permissions ON permission.id = role_permissions.permission_id").
		Joins("JOIN user_roles ON role_permissions.role_id = user_roles.role_id").
		Where("user_roles.user_id = ? AND permission.enabled = ? AND permission.deleted_at IS NULL", userID, true).
		Group("permission.id").
		Find(&permissions).Error

	if err != nil {
		return nil, errorx.WrapError(err, "查询用户权限失败")
	}
	return permissions, nil
}

// GetEnabledPermissions 获取启用的权限列表
func (r *PermissionRepo) GetEnabledPermissions(ctx context.Context) ([]Permission, error) {
	permissions, err := r.List(ctx, 1, 1000, &QueryOptions{
		Conditions: map[string]any{
			"enabled":    true,
			"deleted_at": nil,
		},
		OrderBy: "category ASC, sort ASC, created_at ASC",
	})
	if err != nil {
		return nil, errorx.WrapError(err, "查询启用权限失败")
	}
	return permissions, nil
}

// GetPermissionsByCategory 根据分类获取权限列表
func (r *PermissionRepo) GetPermissionsByCategory(ctx context.Context, category string) ([]Permission, error) {
	permissions, err := r.List(ctx, 1, 1000, &QueryOptions{
		Conditions: map[string]any{
			"category":   category,
			"enabled":    true,
			"deleted_at": nil,
		},
		OrderBy: "sort ASC, created_at ASC",
	})
	if err != nil {
		return nil, errorx.WrapError(err, "查询分类权限失败")
	}
	return permissions, nil
}

// CheckPermission 检查权限是否存在
func (r *PermissionRepo) CheckPermission(ctx context.Context, resource, action string) (bool, error) {
	count, err := r.Count(ctx, &QueryOptions{
		Conditions: map[string]any{
			"resource":   resource,
			"action":     action,
			"enabled":    true,
			"deleted_at": nil,
		},
	})
	if err != nil {
		return false, errorx.WrapError(err, "检查权限失败")
	}
	return count > 0, nil
}

// GetPermissionByResourceAction 根据资源和操作获取权限
func (r *PermissionRepo) GetPermissionByResourceAction(ctx context.Context, resource, action string) (*Permission, error) {
	permissions, err := r.List(ctx, 1, 1, &QueryOptions{
		Conditions: map[string]any{
			"resource":   resource,
			"action":     action,
			"enabled":    true,
			"deleted_at": nil,
		},
	})
	if err != nil {
		return nil, errorx.WrapError(err, "查询权限失败")
	}
	if len(permissions) == 0 {
		return nil, errorx.WrapError(nil, "权限不存在")
	}
	return &permissions[0], nil
}
