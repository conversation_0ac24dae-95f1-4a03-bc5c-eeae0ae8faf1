package permission

import (
	"context"
	"fmt"
	"strings"

	"github.com/limitcool/starter/internal/pkg/logger"
)

// MiddlewareHelper 权限中间件辅助服务
type MiddlewareHelper struct {
	service *Service
}

// NewMiddlewareHelper 创建权限中间件辅助服务
func NewMiddlewareHelper(service *Service) *MiddlewareHelper {
	return &MiddlewareHelper{
		service: service,
	}
}

// CheckUserPermission 检查用户权限
func (h *MiddlewareHelper) CheckUserPermission(ctx context.Context, userID int64, resource, action string) (bool, error) {
	if h.service == nil {
		// 如果权限服务未初始化，默认允许
		return true, nil
	}

	return h.service.CheckPermission(ctx, userID, resource, action)
}

// CheckUserRole 检查用户角色
func (h *MiddlewareHelper) CheckUserRole(ctx context.Context, userID int64, requiredRoles []string) (bool, error) {
	if h.service == nil {
		// 如果权限服务未初始化，默认允许
		return true, nil
	}

	// 获取用户角色
	userRoles, err := h.service.GetUserRoles(ctx, userID)
	if err != nil {
		return false, err
	}

	// 检查用户是否有所需角色
	for _, userRole := range userRoles {
		for _, requiredRole := range requiredRoles {
			if userRole.Name == requiredRole {
				return true, nil
			}
		}
	}

	return false, nil
}

// CheckUserAdmin 检查用户是否是管理员
func (h *MiddlewareHelper) CheckUserAdmin(ctx context.Context, userID int64) (bool, error) {
	if h.service == nil {
		// 如果权限服务未初始化，默认允许
		return true, nil
	}

	return h.service.IsAdmin(ctx, userID)
}

// ExtractResourceAction 从HTTP方法和路径提取资源和操作
func (h *MiddlewareHelper) ExtractResourceAction(method, path string) (resource, action string) {
	// 清理路径，移除参数
	cleanPath := h.cleanPath(path)
	
	// 从路径提取资源
	resource = h.extractResource(cleanPath)
	
	// 从HTTP方法确定操作
	action = h.extractAction(method)
	
	logger.DebugContext(context.Background(), "提取权限信息",
		"method", method,
		"path", path,
		"clean_path", cleanPath,
		"resource", resource,
		"action", action)
	
	return resource, action
}

// cleanPath 清理路径，移除路径参数
func (h *MiddlewareHelper) cleanPath(path string) string {
	// 移除路径参数，例如 /api/v1/users/:id -> /api/v1/users
	pathParts := strings.Split(path, "/")
	var cleanParts []string
	
	for _, part := range pathParts {
		if part != "" && !strings.HasPrefix(part, ":") && !strings.HasPrefix(part, "*") {
			cleanParts = append(cleanParts, part)
		}
	}
	
	return "/" + strings.Join(cleanParts, "/")
}

// extractResource 从路径提取资源
func (h *MiddlewareHelper) extractResource(path string) string {
	// 移除前缀 /api/v1/
	path = strings.TrimPrefix(path, "/api/v1/")
	path = strings.TrimPrefix(path, "/api/")
	path = strings.TrimPrefix(path, "/")
	
	// 移除admin前缀
	path = strings.TrimPrefix(path, "admin/")
	
	// 获取第一个路径段作为资源
	parts := strings.Split(path, "/")
	if len(parts) > 0 && parts[0] != "" {
		return parts[0]
	}
	
	return "unknown"
}

// extractAction 从HTTP方法确定操作
func (h *MiddlewareHelper) extractAction(method string) string {
	switch strings.ToUpper(method) {
	case "GET":
		return "read"
	case "POST":
		return "create"
	case "PUT", "PATCH":
		return "update"
	case "DELETE":
		return "delete"
	default:
		return "access"
	}
}

// BuildPermissionKey 构建权限键
func (h *MiddlewareHelper) BuildPermissionKey(resource, action string) string {
	return fmt.Sprintf("%s:%s", resource, action)
}

// ParsePermissionKey 解析权限键
func (h *MiddlewareHelper) ParsePermissionKey(permissionKey string) (resource, action string) {
	parts := strings.Split(permissionKey, ":")
	if len(parts) == 2 {
		return parts[0], parts[1]
	}
	return permissionKey, "access"
}

// IsPublicPath 检查是否是公开路径
func (h *MiddlewareHelper) IsPublicPath(path string) bool {
	publicPaths := []string{
		"/api/v1/auth/login",
		"/api/v1/auth/register",
		"/api/v1/auth/refresh",
		"/api/v1/health",
		"/api/v1/version",
		"/swagger",
		"/docs",
		"/favicon.ico",
		"/static",
		"/assets",
	}
	
	for _, publicPath := range publicPaths {
		if strings.HasPrefix(path, publicPath) {
			return true
		}
	}
	
	return false
}

// IsAdminPath 检查是否是管理员路径
func (h *MiddlewareHelper) IsAdminPath(path string) bool {
	adminPaths := []string{
		"/api/v1/admin",
	}
	
	for _, adminPath := range adminPaths {
		if strings.HasPrefix(path, adminPath) {
			return true
		}
	}
	
	return false
}

// IsUserPath 检查是否是用户路径
func (h *MiddlewareHelper) IsUserPath(path string) bool {
	userPaths := []string{
		"/api/v1/user",
	}
	
	for _, userPath := range userPaths {
		if strings.HasPrefix(path, userPath) {
			return true
		}
	}
	
	return false
}

// GetPathType 获取路径类型
func (h *MiddlewareHelper) GetPathType(path string) string {
	if h.IsPublicPath(path) {
		return "public"
	}
	if h.IsAdminPath(path) {
		return "admin"
	}
	if h.IsUserPath(path) {
		return "user"
	}
	return "unknown"
}

// LogPermissionCheck 记录权限检查日志
func (h *MiddlewareHelper) LogPermissionCheck(ctx context.Context, userID int64, resource, action string, result bool, err error) {
	if err != nil {
		logger.ErrorContext(ctx, "权限检查失败",
			"user_id", userID,
			"resource", resource,
			"action", action,
			"error", err)
	} else {
		logger.DebugContext(ctx, "权限检查结果",
			"user_id", userID,
			"resource", resource,
			"action", action,
			"result", result)
	}
}

// LogRoleCheck 记录角色检查日志
func (h *MiddlewareHelper) LogRoleCheck(ctx context.Context, userID int64, requiredRoles []string, result bool, err error) {
	if err != nil {
		logger.ErrorContext(ctx, "角色检查失败",
			"user_id", userID,
			"required_roles", requiredRoles,
			"error", err)
	} else {
		logger.DebugContext(ctx, "角色检查结果",
			"user_id", userID,
			"required_roles", requiredRoles,
			"result", result)
	}
}

// LogAdminCheck 记录管理员检查日志
func (h *MiddlewareHelper) LogAdminCheck(ctx context.Context, userID int64, result bool, err error) {
	if err != nil {
		logger.ErrorContext(ctx, "管理员检查失败",
			"user_id", userID,
			"error", err)
	} else {
		logger.DebugContext(ctx, "管理员检查结果",
			"user_id", userID,
			"result", result)
	}
}
