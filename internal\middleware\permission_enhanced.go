package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/limitcool/starter/internal/api/response"
	"github.com/limitcool/starter/internal/pkg/errorx"
	"github.com/limitcool/starter/internal/pkg/logger"
	"github.com/limitcool/starter/internal/pkg/permission"
)

// EnhancedPermissionCheck 增强的权限检查中间件
// 使用路由配置进行权限检查
func EnhancedPermissionCheck(helper *permission.MiddlewareHelper, routeConfig *permission.RouteConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		method := c.Request.Method
		path := c.Request.URL.Path

		// 查找匹配的路由配置
		route := routeConfig.FindMatchingRoute(method, path)
		if route == nil {
			// 如果没有配置，使用动态权限检查
			DynamicPermissionCheck(helper)(c)
			return
		}

		// 如果路由被禁用
		if route.Disabled {
			logger.WarnContext(ctx, "访问被禁用的路由",
				"method", method,
				"path", path)
			response.Error(c, errorx.ErrForbidden.WithMsg("该功能暂时不可用"))
			c.Abort()
			return
		}

		// 如果是公开路由，直接通过
		if route.Public {
			c.Next()
			return
		}

		// 获取用户ID
		userID := GetUserIDInt64(c)
		if userID == 0 {
			logger.WarnContext(ctx, "权限检查失败：用户未登录",
				"method", method,
				"path", path)
			response.Error(c, errorx.ErrUserNoLogin)
			c.Abort()
			return
		}

		// 如果是仅管理员路由
		if route.AdminOnly {
			isAdmin, err := helper.CheckUserAdmin(ctx, userID)
			if err != nil {
				helper.LogAdminCheck(ctx, userID, false, err)
				response.Error(c, errorx.ErrPermissionDenied)
				c.Abort()
				return
			}

			if !isAdmin {
				helper.LogAdminCheck(ctx, userID, false, nil)
				response.Error(c, errorx.ErrPermissionDenied)
				c.Abort()
				return
			}

			helper.LogAdminCheck(ctx, userID, true, nil)
			c.Next()
			return
		}

		// 如果有角色要求
		if len(route.Roles) > 0 {
			hasRole, err := helper.CheckUserRole(ctx, userID, route.Roles)
			if err != nil {
				helper.LogRoleCheck(ctx, userID, route.Roles, false, err)
				response.Error(c, errorx.ErrPermissionDenied)
				c.Abort()
				return
			}

			if !hasRole {
				helper.LogRoleCheck(ctx, userID, route.Roles, false, nil)
				response.Error(c, errorx.ErrPermissionDenied)
				c.Abort()
				return
			}

			helper.LogRoleCheck(ctx, userID, route.Roles, true, nil)
		}

		// 检查具体权限
		if route.Resource != "" && route.Action != "" {
			hasPermission, err := helper.CheckUserPermission(ctx, userID, route.Resource, route.Action)
			if err != nil {
				helper.LogPermissionCheck(ctx, userID, route.Resource, route.Action, false, err)
				response.Error(c, errorx.ErrPermissionDenied)
				c.Abort()
				return
			}

			if !hasPermission {
				helper.LogPermissionCheck(ctx, userID, route.Resource, route.Action, false, nil)
				response.Error(c, errorx.ErrPermissionDenied)
				c.Abort()
				return
			}

			helper.LogPermissionCheck(ctx, userID, route.Resource, route.Action, true, nil)
		}

		// 权限检查通过
		c.Next()
	}
}

// SmartPermissionCheck 智能权限检查中间件
// 根据路径类型自动选择检查策略
func SmartPermissionCheck(helper *permission.MiddlewareHelper, routeConfig *permission.RouteConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		method := c.Request.Method
		path := c.Request.URL.Path

		// 检查是否是公开路径
		if helper.IsPublicPath(path) {
			c.Next()
			return
		}

		// 查找路由配置
		route := routeConfig.FindMatchingRoute(method, path)
		if route != nil {
			// 使用配置的权限检查
			EnhancedPermissionCheck(helper, routeConfig)(c)
			return
		}

		// 获取用户ID
		userID := GetUserIDInt64(c)
		if userID == 0 {
			logger.WarnContext(ctx, "权限检查失败：用户未登录",
				"method", method,
				"path", path)
			response.Error(c, errorx.ErrUserNoLogin)
			c.Abort()
			return
		}

		// 根据路径类型进行检查
		pathType := helper.GetPathType(path)
		switch pathType {
		case "admin":
			// 管理员路径，检查管理员权限
			isAdmin, err := helper.CheckUserAdmin(ctx, userID)
			if err != nil {
				helper.LogAdminCheck(ctx, userID, false, err)
				response.Error(c, errorx.ErrPermissionDenied)
				c.Abort()
				return
			}

			if !isAdmin {
				helper.LogAdminCheck(ctx, userID, false, nil)
				response.Error(c, errorx.ErrPermissionDenied)
				c.Abort()
				return
			}

			helper.LogAdminCheck(ctx, userID, true, nil)

		case "user":
			// 用户路径，只需要登录即可
			logger.DebugContext(ctx, "用户路径访问",
				"user_id", userID,
				"path", path)

		default:
			// 未知路径，使用动态权限检查
			resource, action := helper.ExtractResourceAction(method, c.FullPath())
			hasPermission, err := helper.CheckUserPermission(ctx, userID, resource, action)
			if err != nil {
				helper.LogPermissionCheck(ctx, userID, resource, action, false, err)
				response.Error(c, errorx.ErrPermissionDenied)
				c.Abort()
				return
			}

			if !hasPermission {
				helper.LogPermissionCheck(ctx, userID, resource, action, false, nil)
				response.Error(c, errorx.ErrPermissionDenied)
				c.Abort()
				return
			}

			helper.LogPermissionCheck(ctx, userID, resource, action, true, nil)
		}

		c.Next()
	}
}

// PermissionCheckWithFallback 带回退的权限检查中间件
// 如果权限服务不可用，可以选择允许或拒绝访问
func PermissionCheckWithFallback(helper *permission.MiddlewareHelper, allowOnFailure bool) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()

		// 检查是否是公开路径
		if helper.IsPublicPath(c.Request.URL.Path) {
			c.Next()
			return
		}

		// 获取用户ID
		userID := GetUserIDInt64(c)
		if userID == 0 {
			logger.WarnContext(ctx, "权限检查失败：用户未登录")
			response.Error(c, errorx.ErrUserNoLogin)
			c.Abort()
			return
		}

		// 提取权限信息
		resource, action := helper.ExtractResourceAction(c.Request.Method, c.FullPath())

		// 检查权限
		hasPermission, err := helper.CheckUserPermission(ctx, userID, resource, action)
		if err != nil {
			helper.LogPermissionCheck(ctx, userID, resource, action, false, err)
			
			// 如果权限检查失败，根据配置决定是否允许访问
			if allowOnFailure {
				logger.WarnContext(ctx, "权限检查失败，但允许访问",
					"user_id", userID,
					"resource", resource,
					"action", action,
					"error", err)
				c.Next()
				return
			} else {
				response.Error(c, errorx.ErrPermissionDenied)
				c.Abort()
				return
			}
		}

		if !hasPermission {
			helper.LogPermissionCheck(ctx, userID, resource, action, false, nil)
			response.Error(c, errorx.ErrPermissionDenied)
			c.Abort()
			return
		}

		helper.LogPermissionCheck(ctx, userID, resource, action, true, nil)
		c.Next()
	}
}
