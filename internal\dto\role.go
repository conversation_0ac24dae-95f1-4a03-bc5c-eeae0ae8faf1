package dto

import "time"

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	Name        string `json:"name" binding:"required,max=50" example:"admin"`                    // 角色名称
	DisplayName string `json:"display_name" binding:"required,max=100" example:"系统管理员"`         // 角色显示名称
	Description string `json:"description" binding:"max=500" example:"系统管理员角色，拥有所有权限"`          // 角色描述
	Enabled     *bool  `json:"enabled" example:"true"`                                           // 是否启用
	Sort        *int   `json:"sort" example:"1"`                                                 // 排序
}

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	Name        string `json:"name" binding:"required,max=50" example:"admin"`                    // 角色名称
	DisplayName string `json:"display_name" binding:"required,max=100" example:"系统管理员"`         // 角色显示名称
	Description string `json:"description" binding:"max=500" example:"系统管理员角色，拥有所有权限"`          // 角色描述
	Enabled     *bool  `json:"enabled" example:"true"`                                           // 是否启用
	Sort        *int   `json:"sort" example:"1"`                                                 // 排序
}

// RoleListRequest 角色列表请求
type RoleListRequest struct {
	Page     int    `form:"page" binding:"min=1" example:"1"`                    // 页码
	PageSize int    `form:"page_size" binding:"min=1,max=100" example:"10"`      // 每页数量
	Keyword  string `form:"keyword" example:"admin"`                             // 关键字搜索
	Enabled  *bool  `form:"enabled" example:"true"`                              // 是否启用
}

// AssignPermissionsRequest 分配权限请求
type AssignPermissionsRequest struct {
	PermissionIDs []int64 `json:"permission_ids" binding:"required" example:"[1,2,3]"` // 权限ID列表
}

// AssignUsersRequest 分配用户请求
type AssignUsersRequest struct {
	UserIDs []int64 `json:"user_ids" binding:"required" example:"[1,2,3]"` // 用户ID列表
}

// RoleResponse 角色响应
type RoleResponse struct {
	ID          int64     `json:"id" example:"1"`                                      // 角色ID
	Name        string    `json:"name" example:"admin"`                                // 角色名称
	DisplayName string    `json:"display_name" example:"系统管理员"`                       // 角色显示名称
	Description string    `json:"description" example:"系统管理员角色，拥有所有权限"`                // 角色描述
	Enabled     bool      `json:"enabled" example:"true"`                              // 是否启用
	IsSystem    bool      `json:"is_system" example:"false"`                           // 是否系统角色
	Sort        int       `json:"sort" example:"1"`                                    // 排序
	CreatedBy   int64     `json:"created_by" example:"1"`                              // 创建者ID
	UpdatedBy   int64     `json:"updated_by" example:"1"`                              // 更新者ID
	CreatedAt   time.Time `json:"created_at" example:"2023-01-01T00:00:00Z"`           // 创建时间
	UpdatedAt   time.Time `json:"updated_at" example:"2023-01-01T00:00:00Z"`           // 更新时间
}

// RoleDetailResponse 角色详情响应
type RoleDetailResponse struct {
	RoleResponse
	Permissions []PermissionResponse `json:"permissions"` // 角色权限列表
	Users       []UserSimpleResponse `json:"users"`       // 角色用户列表
}

// RoleListResponse 角色列表响应
type RoleListResponse struct {
	List  []RoleResponse `json:"list"`  // 角色列表
	Total int64          `json:"total"` // 总数
}

// RoleSimpleResponse 角色简单响应
type RoleSimpleResponse struct {
	ID          int64  `json:"id" example:"1"`           // 角色ID
	Name        string `json:"name" example:"admin"`     // 角色名称
	DisplayName string `json:"display_name" example:"系统管理员"` // 角色显示名称
	Enabled     bool   `json:"enabled" example:"true"`   // 是否启用
}
