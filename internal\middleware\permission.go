package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/limitcool/starter/internal/api/response"
	"github.com/limitcool/starter/internal/pkg/errorx"
	"github.com/limitcool/starter/internal/pkg/logger"
	"github.com/limitcool/starter/internal/pkg/permission"
)

// PermissionCheck 权限检查中间件
func PermissionCheck(helper *permission.MiddlewareHelper, resource, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()

		// 获取用户ID
		userID := GetUserIDInt64(c)
		if userID == 0 {
			logger.WarnContext(ctx, "权限检查失败：用户未登录")
			response.Error(c, errorx.ErrUserNoLogin)
			c.Abort()
			return
		}

		// 检查权限
		hasPermission, err := helper.CheckUserPermission(ctx, userID, resource, action)
		if err != nil {
			helper.LogPermissionCheck(ctx, userID, resource, action, false, err)
			response.Error(c, errorx.ErrPermissionDenied)
			c.Abort()
			return
		}

		if !hasPermission {
			helper.LogPermissionCheck(ctx, userID, resource, action, false, nil)
			response.Error(c, errorx.ErrPermissionDenied)
			c.Abort()
			return
		}

		// 权限检查通过，继续处理请求
		helper.LogPermissionCheck(ctx, userID, resource, action, true, nil)
		c.Next()
	}
}

// DynamicPermissionCheck 动态权限检查中间件
// 从请求路径和方法动态确定权限
func DynamicPermissionCheck(helper *permission.MiddlewareHelper) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()

		// 检查是否是公开路径
		if helper.IsPublicPath(c.Request.URL.Path) {
			c.Next()
			return
		}

		// 获取用户ID
		userID := GetUserIDInt64(c)
		if userID == 0 {
			logger.WarnContext(ctx, "权限检查失败：用户未登录")
			response.Error(c, errorx.ErrUserNoLogin)
			c.Abort()
			return
		}

		// 从请求路径和方法确定资源和操作
		resource, action := helper.ExtractResourceAction(c.Request.Method, c.FullPath())

		// 检查权限
		hasPermission, err := helper.CheckUserPermission(ctx, userID, resource, action)
		if err != nil {
			helper.LogPermissionCheck(ctx, userID, resource, action, false, err)
			response.Error(c, errorx.ErrPermissionDenied)
			c.Abort()
			return
		}

		if !hasPermission {
			helper.LogPermissionCheck(ctx, userID, resource, action, false, nil)
			response.Error(c, errorx.ErrPermissionDenied)
			c.Abort()
			return
		}

		// 权限检查通过，继续处理请求
		helper.LogPermissionCheck(ctx, userID, resource, action, true, nil)
		c.Next()
	}
}

// AdminOnlyCheck 仅管理员访问中间件
func AdminOnlyCheck(helper *permission.MiddlewareHelper) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()

		// 获取用户ID
		userID := GetUserIDInt64(c)
		if userID == 0 {
			logger.WarnContext(ctx, "管理员检查失败：用户未登录")
			response.Error(c, errorx.ErrUserNoLogin)
			c.Abort()
			return
		}

		// 检查是否是管理员
		isAdmin, err := helper.CheckUserAdmin(ctx, userID)
		if err != nil {
			helper.LogAdminCheck(ctx, userID, false, err)
			response.Error(c, errorx.ErrPermissionDenied)
			c.Abort()
			return
		}

		if !isAdmin {
			helper.LogAdminCheck(ctx, userID, false, nil)
			response.Error(c, errorx.ErrPermissionDenied)
			c.Abort()
			return
		}

		// 管理员检查通过，继续处理请求
		helper.LogAdminCheck(ctx, userID, true, nil)
		c.Next()
	}
}

// RoleCheck 角色检查中间件
func RoleCheck(helper *permission.MiddlewareHelper, requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()

		// 获取用户ID
		userID := GetUserIDInt64(c)
		if userID == 0 {
			logger.WarnContext(ctx, "角色检查失败：用户未登录")
			response.Error(c, errorx.ErrUserNoLogin)
			c.Abort()
			return
		}

		// 检查用户角色
		hasRole, err := helper.CheckUserRole(ctx, userID, requiredRoles)
		if err != nil {
			helper.LogRoleCheck(ctx, userID, requiredRoles, false, err)
			response.Error(c, errorx.ErrPermissionDenied)
			c.Abort()
			return
		}

		if !hasRole {
			helper.LogRoleCheck(ctx, userID, requiredRoles, false, nil)
			response.Error(c, errorx.ErrPermissionDenied)
			c.Abort()
			return
		}

		// 角色检查通过，继续处理请求
		helper.LogRoleCheck(ctx, userID, requiredRoles, true, nil)
		c.Next()
	}
}
