package handler

import (
	"github.com/gin-gonic/gin"
	"github.com/limitcool/starter/internal/api/response"
	"github.com/limitcool/starter/internal/dto"
	"github.com/limitcool/starter/internal/model"
	"github.com/limitcool/starter/internal/pkg/errorx"
	"github.com/limitcool/starter/internal/pkg/logger"
)

// ListPermissions 获取权限列表
// @Summary 获取权限列表
// @Description 获取权限列表，支持分页和搜索
// @Tags 权限管理
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "关键字搜索"
// @Param category query string false "权限分类"
// @Param resource query string false "资源标识"
// @Param enabled query bool false "是否启用"
// @Success 200 {object} response.Response{data=dto.PermissionListResponse} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/admin/permissions [get]
func (h *PermissionHandler) ListPermissions(c *gin.Context) {
	ctx := c.Request.Context()
	var req dto.PermissionListRequest

	if err := c.ShouldBindQuery(&req); err != nil {
		logger.WarnContext(ctx, "权限列表参数绑定失败", "error", err)
		response.Error(c, errorx.ErrInvalidParams.WithMsg("请求参数错误"))
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	opts := &model.QueryOptions{
		OrderBy: "category ASC, sort ASC, created_at DESC",
	}

	conditions := make(map[string]any)
	if req.Enabled != nil {
		conditions["enabled"] = *req.Enabled
	}
	if req.Category != "" {
		conditions["category"] = req.Category
	}
	if req.Resource != "" {
		conditions["resource"] = req.Resource
	}

	// 关键字搜索
	if req.Keyword != "" {
		opts.Condition = "name LIKE ? OR display_name LIKE ? OR description LIKE ? OR resource LIKE ?"
		opts.Args = []any{
			"%" + req.Keyword + "%",
			"%" + req.Keyword + "%",
			"%" + req.Keyword + "%",
			"%" + req.Keyword + "%",
		}
	}

	if len(conditions) > 0 {
		opts.Conditions = conditions
	}

	// 获取权限列表
	permissions, err := h.permissionRepo.List(ctx, req.Page, req.PageSize, opts)
	if err != nil {
		logger.ErrorContext(ctx, "获取权限列表失败", "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("获取权限列表失败"))
		return
	}

	// 获取总数
	total, err := h.permissionRepo.Count(ctx, opts)
	if err != nil {
		logger.ErrorContext(ctx, "获取权限总数失败", "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("获取权限总数失败"))
		return
	}

	// 转换为响应格式
	var permissionList []dto.PermissionResponse
	for _, perm := range permissions {
		permissionList = append(permissionList, dto.PermissionResponse{
			ID:          perm.ID,
			Name:        perm.Name,
			DisplayName: perm.DisplayName,
			Description: perm.Description,
			Resource:    perm.Resource,
			Action:      perm.Action,
			Method:      perm.Method,
			Path:        perm.Path,
			Category:    perm.Category,
			Enabled:     perm.Enabled,
			IsSystem:    perm.IsSystem,
			Sort:        perm.Sort,
			CreatedBy:   perm.CreatedBy,
			UpdatedBy:   perm.UpdatedBy,
			CreatedAt:   perm.CreatedAt,
			UpdatedAt:   perm.UpdatedAt,
		})
	}

	resp := &dto.PermissionListResponse{
		List:  permissionList,
		Total: total,
	}

	response.Success(c, resp)
}

// ListPermissionsByCategory 按分类获取权限列表
// @Summary 按分类获取权限列表
// @Description 按分类获取权限列表，用于权限分配时的分组显示
// @Tags 权限管理
// @Produce json
// @Success 200 {object} response.Response{data=[]dto.PermissionCategoryResponse} "获取成功"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/admin/permissions/categories [get]
func (h *PermissionHandler) ListPermissionsByCategory(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取所有启用的权限
	permissions, err := h.permissionRepo.GetEnabledPermissions(ctx)
	if err != nil {
		logger.ErrorContext(ctx, "获取权限列表失败", "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("获取权限列表失败"))
		return
	}

	// 按分类分组
	categoryMap := make(map[string][]dto.PermissionSimpleResponse)
	for _, perm := range permissions {
		category := perm.Category
		if category == "" {
			category = "其他"
		}

		permSimple := dto.PermissionSimpleResponse{
			ID:          perm.ID,
			Name:        perm.Name,
			DisplayName: perm.DisplayName,
			Resource:    perm.Resource,
			Action:      perm.Action,
			Category:    perm.Category,
			Enabled:     perm.Enabled,
		}

		categoryMap[category] = append(categoryMap[category], permSimple)
	}

	// 转换为响应格式
	var categories []dto.PermissionCategoryResponse
	for category, perms := range categoryMap {
		categories = append(categories, dto.PermissionCategoryResponse{
			Category:    category,
			Permissions: perms,
		})
	}

	response.Success(c, categories)
}

// GetPermissionOptions 获取权限选项
// @Summary 获取权限选项
// @Description 获取权限选项，用于下拉选择
// @Tags 权限管理
// @Produce json
// @Param enabled query bool false "是否启用" default(true)
// @Success 200 {object} response.Response{data=[]dto.PermissionSimpleResponse} "获取成功"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/admin/permissions/options [get]
func (h *PermissionHandler) GetPermissionOptions(c *gin.Context) {
	ctx := c.Request.Context()

	enabled := true
	if enabledParam := c.Query("enabled"); enabledParam == "false" {
		enabled = false
	}

	// 构建查询条件
	opts := &model.QueryOptions{
		Conditions: map[string]any{
			"enabled": enabled,
		},
		OrderBy: "category ASC, sort ASC, name ASC",
	}

	// 获取权限列表
	permissions, err := h.permissionRepo.List(ctx, 1, 1000, opts)
	if err != nil {
		logger.ErrorContext(ctx, "获取权限选项失败", "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("获取权限选项失败"))
		return
	}

	// 转换为简单响应格式
	var options []dto.PermissionSimpleResponse
	for _, perm := range permissions {
		options = append(options, dto.PermissionSimpleResponse{
			ID:          perm.ID,
			Name:        perm.Name,
			DisplayName: perm.DisplayName,
			Resource:    perm.Resource,
			Action:      perm.Action,
			Category:    perm.Category,
			Enabled:     perm.Enabled,
		})
	}

	response.Success(c, options)
}
