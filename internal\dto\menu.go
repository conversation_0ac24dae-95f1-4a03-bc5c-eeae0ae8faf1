package dto

import "time"

// CreateMenuRequest 创建菜单请求
type CreateMenuRequest struct {
	Name       string `json:"name" binding:"required,max=100" example:"user-management"`        // 菜单名称
	Title      string `json:"title" binding:"required,max=100" example:"用户管理"`                 // 菜单标题
	Icon       string `json:"icon" binding:"max=100" example:"user"`                            // 菜单图标
	Path       string `json:"path" binding:"max=255" example:"/users"`                          // 菜单路径
	Component  string `json:"component" binding:"max=255" example:"@/views/users/index.vue"`    // 组件路径
	ParentID   int64  `json:"parent_id" example:"0"`                                            // 父菜单ID
	Type       string `json:"type" binding:"required,oneof=menu button" example:"menu"`        // 菜单类型
	Permission string `json:"permission" binding:"max=100" example:"user:read"`                 // 权限标识
	Sort       *int   `json:"sort" example:"1"`                                                 // 排序
	Visible    *bool  `json:"visible" example:"true"`                                           // 是否可见
	Enabled    *bool  `json:"enabled" example:"true"`                                           // 是否启用
	KeepAlive  *bool  `json:"keep_alive" example:"false"`                                       // 是否缓存
	External   *bool  `json:"external" example:"false"`                                         // 是否外链
	Redirect   string `json:"redirect" binding:"max=255" example:"/users/list"`                 // 重定向路径
	Meta       string `json:"meta" example:"{\"title\":\"用户管理\"}"`                             // 元数据
}

// UpdateMenuRequest 更新菜单请求
type UpdateMenuRequest struct {
	Name       string `json:"name" binding:"required,max=100" example:"user-management"`        // 菜单名称
	Title      string `json:"title" binding:"required,max=100" example:"用户管理"`                 // 菜单标题
	Icon       string `json:"icon" binding:"max=100" example:"user"`                            // 菜单图标
	Path       string `json:"path" binding:"max=255" example:"/users"`                          // 菜单路径
	Component  string `json:"component" binding:"max=255" example:"@/views/users/index.vue"`    // 组件路径
	ParentID   int64  `json:"parent_id" example:"0"`                                            // 父菜单ID
	Type       string `json:"type" binding:"required,oneof=menu button" example:"menu"`        // 菜单类型
	Permission string `json:"permission" binding:"max=100" example:"user:read"`                 // 权限标识
	Sort       *int   `json:"sort" example:"1"`                                                 // 排序
	Visible    *bool  `json:"visible" example:"true"`                                           // 是否可见
	Enabled    *bool  `json:"enabled" example:"true"`                                           // 是否启用
	KeepAlive  *bool  `json:"keep_alive" example:"false"`                                       // 是否缓存
	External   *bool  `json:"external" example:"false"`                                         // 是否外链
	Redirect   string `json:"redirect" binding:"max=255" example:"/users/list"`                 // 重定向路径
	Meta       string `json:"meta" example:"{\"title\":\"用户管理\"}"`                             // 元数据
}

// MenuListRequest 菜单列表请求
type MenuListRequest struct {
	ParentID int64  `form:"parent_id" example:"0"`                      // 父菜单ID
	Type     string `form:"type" example:"menu"`                        // 菜单类型
	Visible  *bool  `form:"visible" example:"true"`                     // 是否可见
	Enabled  *bool  `form:"enabled" example:"true"`                     // 是否启用
}

// AssignPermissionsToMenuRequest 为菜单分配权限请求
type AssignPermissionsToMenuRequest struct {
	PermissionIDs []int64 `json:"permission_ids" binding:"required" example:"[1,2,3]"` // 权限ID列表
}

// MenuResponse 菜单响应
type MenuResponse struct {
	ID         int64     `json:"id" example:"1"`                                      // 菜单ID
	Name       string    `json:"name" example:"user-management"`                      // 菜单名称
	Title      string    `json:"title" example:"用户管理"`                               // 菜单标题
	Icon       string    `json:"icon" example:"user"`                                 // 菜单图标
	Path       string    `json:"path" example:"/users"`                               // 菜单路径
	Component  string    `json:"component" example:"@/views/users/index.vue"`         // 组件路径
	ParentID   int64     `json:"parent_id" example:"0"`                               // 父菜单ID
	Type       string    `json:"type" example:"menu"`                                 // 菜单类型
	Permission string    `json:"permission" example:"user:read"`                      // 权限标识
	Sort       int       `json:"sort" example:"1"`                                    // 排序
	Visible    bool      `json:"visible" example:"true"`                              // 是否可见
	Enabled    bool      `json:"enabled" example:"true"`                              // 是否启用
	IsSystem   bool      `json:"is_system" example:"false"`                           // 是否系统菜单
	KeepAlive  bool      `json:"keep_alive" example:"false"`                          // 是否缓存
	External   bool      `json:"external" example:"false"`                            // 是否外链
	Redirect   string    `json:"redirect" example:"/users/list"`                      // 重定向路径
	Meta       string    `json:"meta" example:"{\"title\":\"用户管理\"}"`                // 元数据
	CreatedBy  int64     `json:"created_by" example:"1"`                              // 创建者ID
	UpdatedBy  int64     `json:"updated_by" example:"1"`                              // 更新者ID
	CreatedAt  time.Time `json:"created_at" example:"2023-01-01T00:00:00Z"`           // 创建时间
	UpdatedAt  time.Time `json:"updated_at" example:"2023-01-01T00:00:00Z"`           // 更新时间
}

// MenuTreeResponse 菜单树响应
type MenuTreeResponse struct {
	MenuResponse
	Children []MenuTreeResponse `json:"children"` // 子菜单列表
}

// MenuDetailResponse 菜单详情响应
type MenuDetailResponse struct {
	MenuResponse
	Permissions []PermissionSimpleResponse `json:"permissions"` // 菜单权限列表
}

// MenuSimpleResponse 菜单简单响应
type MenuSimpleResponse struct {
	ID       int64  `json:"id" example:"1"`                 // 菜单ID
	Name     string `json:"name" example:"user-management"` // 菜单名称
	Title    string `json:"title" example:"用户管理"`          // 菜单标题
	ParentID int64  `json:"parent_id" example:"0"`          // 父菜单ID
	Type     string `json:"type" example:"menu"`            // 菜单类型
	Visible  bool   `json:"visible" example:"true"`         // 是否可见
	Enabled  bool   `json:"enabled" example:"true"`         // 是否启用
}

// UserMenuResponse 用户菜单响应
type UserMenuResponse struct {
	ID        int64              `json:"id" example:"1"`                 // 菜单ID
	Name      string             `json:"name" example:"user-management"` // 菜单名称
	Title     string             `json:"title" example:"用户管理"`          // 菜单标题
	Icon      string             `json:"icon" example:"user"`            // 菜单图标
	Path      string             `json:"path" example:"/users"`          // 菜单路径
	Component string             `json:"component" example:"@/views/users/index.vue"` // 组件路径
	Type      string             `json:"type" example:"menu"`            // 菜单类型
	Sort      int                `json:"sort" example:"1"`               // 排序
	KeepAlive bool               `json:"keep_alive" example:"false"`     // 是否缓存
	External  bool               `json:"external" example:"false"`       // 是否外链
	Redirect  string             `json:"redirect" example:"/users/list"` // 重定向路径
	Meta      map[string]any     `json:"meta"`                           // 元数据
	Children  []UserMenuResponse `json:"children"`                       // 子菜单列表
}
