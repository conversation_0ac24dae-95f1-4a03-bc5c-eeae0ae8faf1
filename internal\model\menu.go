package model

import (
	"context"
	"time"

	"github.com/limitcool/starter/internal/pkg/errorx"
	"gorm.io/gorm"
)

// Menu 菜单模型
type Menu struct {
	SnowflakeModel

	Name       string     `json:"name" gorm:"size:100;not null;comment:菜单名称"`
	Title      string     `json:"title" gorm:"size:100;not null;comment:菜单标题"`
	Icon       string     `json:"icon" gorm:"size:100;comment:菜单图标"`
	Path       string     `json:"path" gorm:"size:255;comment:菜单路径"`
	Component  string     `json:"component" gorm:"size:255;comment:组件路径"`
	ParentID   int64      `json:"parent_id" gorm:"default:0;comment:父菜单ID"`
	Type       string     `json:"type" gorm:"size:20;not null;default:'menu';comment:菜单类型(menu/button)"`
	Permission string     `json:"permission" gorm:"size:100;comment:权限标识"`
	Sort       int        `json:"sort" gorm:"default:0;comment:排序"`
	Visible    bool       `json:"visible" gorm:"default:true;comment:是否可见"`
	Enabled    bool       `json:"enabled" gorm:"default:true;comment:是否启用"`
	IsSystem   bool       `json:"is_system" gorm:"default:false;comment:是否系统菜单"`
	KeepAlive  bool       `json:"keep_alive" gorm:"default:false;comment:是否缓存"`
	External   bool       `json:"external" gorm:"default:false;comment:是否外链"`
	Redirect   string     `json:"redirect" gorm:"size:255;comment:重定向路径"`
	Meta       string     `json:"meta" gorm:"type:text;comment:元数据(JSON格式)"`
	CreatedBy  int64      `json:"created_by" gorm:"comment:创建者ID"`
	UpdatedBy  int64      `json:"updated_by" gorm:"comment:更新者ID"`
	DeletedAt  *time.Time `json:"deleted_at" gorm:"index;comment:删除时间"`

	// 关联关系
	Parent      *Menu        `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Children    []Menu       `json:"children,omitempty" gorm:"foreignKey:ParentID"`
	Permissions []Permission `json:"permissions,omitempty" gorm:"many2many:menu_permissions;"`
}

func (Menu) TableName() string {
	return "menu"
}

func NewMenu() *Menu {
	return &Menu{}
}

// MenuRepo 菜单仓库
type MenuRepo struct {
	*GenericRepo[Menu]
}

// NewMenuRepo 创建菜单仓库
func NewMenuRepo(db *gorm.DB) *MenuRepo {
	return &MenuRepo{
		GenericRepo: NewGenericRepo[Menu](db),
	}
}

// GetMenuTree 获取菜单树
func (r *MenuRepo) GetMenuTree(ctx context.Context, parentID int64) ([]Menu, error) {
	var menus []Menu
	err := r.DB.WithContext(ctx).
		Where("parent_id = ? AND enabled = ? AND deleted_at IS NULL", parentID, true).
		Order("sort ASC, created_at ASC").
		Find(&menus).Error

	if err != nil {
		return nil, errorx.WrapError(err, "查询菜单树失败")
	}

	// 递归获取子菜单
	for i := range menus {
		children, err := r.GetMenuTree(ctx, menus[i].ID)
		if err != nil {
			return nil, err
		}
		menus[i].Children = children
	}

	return menus, nil
}

// GetMenusByUserID 根据用户ID获取可访问的菜单
func (r *MenuRepo) GetMenusByUserID(ctx context.Context, userID int64) ([]Menu, error) {
	var menus []Menu
	err := r.DB.WithContext(ctx).
		Table("menu").
		Joins("JOIN menu_permissions ON menu.id = menu_permissions.menu_id").
		Joins("JOIN role_permissions ON menu_permissions.permission_id = role_permissions.permission_id").
		Joins("JOIN user_roles ON role_permissions.role_id = user_roles.role_id").
		Where("user_roles.user_id = ? AND menu.enabled = ? AND menu.visible = ? AND menu.deleted_at IS NULL", userID, true, true).
		Group("menu.id").
		Order("menu.sort ASC, menu.created_at ASC").
		Find(&menus).Error

	if err != nil {
		return nil, errorx.WrapError(err, "查询用户菜单失败")
	}
	return menus, nil
}

// GetUserMenuTree 获取用户菜单树
func (r *MenuRepo) GetUserMenuTree(ctx context.Context, userID int64) ([]Menu, error) {
	// 获取用户所有可访问的菜单
	userMenus, err := r.GetMenusByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 构建菜单映射
	menuMap := make(map[int64]*Menu)
	for i := range userMenus {
		menuMap[userMenus[i].ID] = &userMenus[i]
	}

	// 构建树形结构
	var rootMenus []Menu
	for _, menu := range userMenus {
		if menu.ParentID == 0 {
			// 根菜单
			rootMenus = append(rootMenus, menu)
		} else {
			// 子菜单
			if parent, exists := menuMap[menu.ParentID]; exists {
				parent.Children = append(parent.Children, menu)
			}
		}
	}

	return rootMenus, nil
}

// GetVisibleMenus 获取可见菜单列表
func (r *MenuRepo) GetVisibleMenus(ctx context.Context) ([]Menu, error) {
	menus, err := r.List(ctx, &QueryOptions{
		Conditions: map[string]any{
			"visible":    true,
			"enabled":    true,
			"deleted_at": nil,
		},
		OrderBy: "sort ASC, created_at ASC",
	})
	if err != nil {
		return nil, errorx.WrapError(err, "查询可见菜单失败")
	}
	return menus, nil
}

// GetMenusByType 根据类型获取菜单
func (r *MenuRepo) GetMenusByType(ctx context.Context, menuType string) ([]Menu, error) {
	menus, err := r.List(ctx, &QueryOptions{
		Conditions: map[string]any{
			"type":       menuType,
			"enabled":    true,
			"deleted_at": nil,
		},
		OrderBy: "sort ASC, created_at ASC",
	})
	if err != nil {
		return nil, errorx.WrapError(err, "查询类型菜单失败")
	}
	return menus, nil
}

// AssignPermissions 为菜单分配权限
func (r *MenuRepo) AssignPermissions(ctx context.Context, menuID int64, permissionIDs []int64) error {
	// 使用事务处理
	return r.Transaction(ctx, func(tx *gorm.DB) error {
		// 先清除现有权限
		err := tx.WithContext(ctx).
			Exec("DELETE FROM menu_permissions WHERE menu_id = ?", menuID).Error
		if err != nil {
			return errorx.WrapError(err, "清除菜单权限失败")
		}

		// 添加新权限
		if len(permissionIDs) > 0 {
			var values []map[string]any
			for _, permID := range permissionIDs {
				values = append(values, map[string]any{
					"menu_id":       menuID,
					"permission_id": permID,
				})
			}
			err = tx.WithContext(ctx).
				Table("menu_permissions").
				Create(values).Error
			if err != nil {
				return errorx.WrapError(err, "分配菜单权限失败")
			}
		}
		return nil
	})
}
