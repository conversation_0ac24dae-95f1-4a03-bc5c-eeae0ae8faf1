package model

import (
	"context"
	"time"

	"github.com/limitcool/starter/internal/pkg/errorx"
	"gorm.io/gorm"
)

// Role 角色模型
type Role struct {
	SnowflakeModel

	Name        string     `json:"name" gorm:"size:50;not null;unique;comment:角色名称"`
	DisplayName string     `json:"display_name" gorm:"size:100;not null;comment:角色显示名称"`
	Description string     `json:"description" gorm:"size:500;comment:角色描述"`
	Enabled     bool       `json:"enabled" gorm:"default:true;comment:是否启用"`
	IsSystem    bool       `json:"is_system" gorm:"default:false;comment:是否系统角色"`
	Sort        int        `json:"sort" gorm:"default:0;comment:排序"`
	CreatedBy   int64      `json:"created_by" gorm:"comment:创建者ID"`
	UpdatedBy   int64      `json:"updated_by" gorm:"comment:更新者ID"`
	DeletedAt   *time.Time `json:"deleted_at" gorm:"index;comment:删除时间"`

	// 关联关系
	Users       []User       `json:"users,omitempty" gorm:"many2many:user_roles;"`
	Permissions []Permission `json:"permissions,omitempty" gorm:"many2many:role_permissions;"`
}

func (Role) TableName() string {
	return "role"
}

func NewRole() *Role {
	return &Role{}
}

// RoleRepo 角色仓库
type RoleRepo struct {
	*GenericRepo[Role]
}

// NewRoleRepo 创建角色仓库
func NewRoleRepo(db *gorm.DB) *RoleRepo {
	return &RoleRepo{
		GenericRepo: NewGenericRepo[Role](db),
	}
}

// GetRoleWithPermissions 获取角色及其权限
func (r *RoleRepo) GetRoleWithPermissions(ctx context.Context, id int64) (*Role, error) {
	role, err := r.Get(ctx, id, &QueryOptions{
		Preloads: []string{"Permissions"},
	})
	if err != nil {
		return nil, errorx.WrapError(err, "查询角色权限失败")
	}
	return role, nil
}

// GetRolesByUserID 根据用户ID获取角色列表
func (r *RoleRepo) GetRolesByUserID(ctx context.Context, userID int64) ([]Role, error) {
	var roles []Role
	err := r.DB.WithContext(ctx).
		Table("role").
		Joins("JOIN user_roles ON role.id = user_roles.role_id").
		Where("user_roles.user_id = ? AND role.enabled = ? AND role.deleted_at IS NULL", userID, true).
		Find(&roles).Error

	if err != nil {
		return nil, errorx.WrapError(err, "查询用户角色失败")
	}
	return roles, nil
}

// GetEnabledRoles 获取启用的角色列表
func (r *RoleRepo) GetEnabledRoles(ctx context.Context) ([]Role, error) {
	roles, err := r.List(ctx, 1, 1000, &QueryOptions{
		Conditions: map[string]any{
			"enabled":    true,
			"deleted_at": nil,
		},
		OrderBy: "sort ASC, created_at ASC",
	})
	if err != nil {
		return nil, errorx.WrapError(err, "查询启用角色失败")
	}
	return roles, nil
}

// AssignPermissions 为角色分配权限
func (r *RoleRepo) AssignPermissions(ctx context.Context, roleID int64, permissionIDs []int64) error {
	// 使用事务处理
	return r.Transaction(ctx, func(tx *gorm.DB) error {
		// 先清除现有权限
		err := tx.WithContext(ctx).
			Exec("DELETE FROM role_permissions WHERE role_id = ?", roleID).Error
		if err != nil {
			return errorx.WrapError(err, "清除角色权限失败")
		}

		// 添加新权限
		if len(permissionIDs) > 0 {
			var values []map[string]any
			for _, permID := range permissionIDs {
				values = append(values, map[string]any{
					"role_id":       roleID,
					"permission_id": permID,
				})
			}
			err = tx.WithContext(ctx).
				Table("role_permissions").
				Create(values).Error
			if err != nil {
				return errorx.WrapError(err, "分配角色权限失败")
			}
		}
		return nil
	})
}
