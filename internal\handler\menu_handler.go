package handler

import (
	"encoding/json"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/limitcool/starter/internal/api/response"
	"github.com/limitcool/starter/internal/dto"
	"github.com/limitcool/starter/internal/middleware"
	"github.com/limitcool/starter/internal/model"
	"github.com/limitcool/starter/internal/pkg/errorx"
	"github.com/limitcool/starter/internal/pkg/logger"
	"github.com/limitcool/starter/internal/pkg/permission"
)

// MenuHandler 菜单处理器
type MenuHandler struct {
	menuRepo          *model.MenuRepo
	permissionService *permission.Service
}

// NewMenuHandler 创建菜单处理器
func NewMenuHandler(menuRepo *model.MenuRepo, permissionService *permission.Service) *MenuHandler {
	return &MenuHandler{
		menuRepo:          menuRepo,
		permissionService: permissionService,
	}
}

// CreateMenu 创建菜单
// @Summary 创建菜单
// @Description 创建新的菜单
// @Tags 菜单管理
// @Accept json
// @Produce json
// @Param request body dto.CreateMenuRequest true "创建菜单请求"
// @Success 200 {object} response.Response{data=dto.MenuResponse} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/admin/menus [post]
func (h *MenuHandler) CreateMenu(c *gin.Context) {
	ctx := c.Request.Context()
	var req dto.CreateMenuRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.WarnContext(ctx, "创建菜单参数绑定失败", "error", err)
		response.Error(c, errorx.ErrInvalidParams.WithMsg("请求参数错误"))
		return
	}

	// 检查菜单名是否已存在
	existingMenu, err := h.menuRepo.List(ctx, 1, 1, &model.QueryOptions{
		Conditions: map[string]any{
			"name": req.Name,
		},
	})
	if err != nil {
		logger.ErrorContext(ctx, "检查菜单名失败", "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("检查菜单名失败"))
		return
	}
	if len(existingMenu) > 0 {
		response.Error(c, errorx.ErrInvalidParams.WithMsg("菜单名已存在"))
		return
	}

	// 如果有父菜单，检查父菜单是否存在
	if req.ParentID > 0 {
		_, err := h.menuRepo.Get(ctx, req.ParentID, nil)
		if err != nil {
			response.Error(c, errorx.ErrInvalidParams.WithMsg("父菜单不存在"))
			return
		}
	}

	// 创建菜单
	menu := &model.Menu{
		Name:       req.Name,
		Title:      req.Title,
		Icon:       req.Icon,
		Path:       req.Path,
		Component:  req.Component,
		ParentID:   req.ParentID,
		Type:       req.Type,
		Permission: req.Permission,
		Sort:       0,
		Visible:    true,
		Enabled:    true,
		KeepAlive:  false,
		External:   false,
		Redirect:   req.Redirect,
		Meta:       req.Meta,
		CreatedBy:  middleware.GetUserIDInt64(c),
		UpdatedBy:  middleware.GetUserIDInt64(c),
	}

	if req.Sort != nil {
		menu.Sort = *req.Sort
	}
	if req.Visible != nil {
		menu.Visible = *req.Visible
	}
	if req.Enabled != nil {
		menu.Enabled = *req.Enabled
	}
	if req.KeepAlive != nil {
		menu.KeepAlive = *req.KeepAlive
	}
	if req.External != nil {
		menu.External = *req.External
	}

	err = h.menuRepo.Create(ctx, menu)
	if err != nil {
		logger.ErrorContext(ctx, "创建菜单失败", "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("创建菜单失败"))
		return
	}

	// 转换为响应格式
	menuResp := &dto.MenuResponse{
		ID:         menu.ID,
		Name:       menu.Name,
		Title:      menu.Title,
		Icon:       menu.Icon,
		Path:       menu.Path,
		Component:  menu.Component,
		ParentID:   menu.ParentID,
		Type:       menu.Type,
		Permission: menu.Permission,
		Sort:       menu.Sort,
		Visible:    menu.Visible,
		Enabled:    menu.Enabled,
		IsSystem:   menu.IsSystem,
		KeepAlive:  menu.KeepAlive,
		External:   menu.External,
		Redirect:   menu.Redirect,
		Meta:       menu.Meta,
		CreatedBy:  menu.CreatedBy,
		UpdatedBy:  menu.UpdatedBy,
		CreatedAt:  menu.CreatedAt,
		UpdatedAt:  menu.UpdatedAt,
	}

	logger.InfoContext(ctx, "创建菜单成功", "menu_id", menu.ID, "menu_name", menu.Name)
	response.Success(c, menuResp)
}

// UpdateMenu 更新菜单
// @Summary 更新菜单
// @Description 更新菜单信息
// @Tags 菜单管理
// @Accept json
// @Produce json
// @Param id path int true "菜单ID"
// @Param request body dto.UpdateMenuRequest true "更新菜单请求"
// @Success 200 {object} response.Response{data=dto.MenuResponse} "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "菜单不存在"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/admin/menus/{id} [put]
func (h *MenuHandler) UpdateMenu(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取菜单ID
	menuID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.Error(c, errorx.ErrInvalidParams.WithMsg("无效的菜单ID"))
		return
	}

	var req dto.UpdateMenuRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.WarnContext(ctx, "更新菜单参数绑定失败", "error", err)
		response.Error(c, errorx.ErrInvalidParams.WithMsg("请求参数错误"))
		return
	}

	// 获取现有菜单
	menu, err := h.menuRepo.Get(ctx, menuID, nil)
	if err != nil {
		logger.ErrorContext(ctx, "获取菜单失败", "menu_id", menuID, "error", err)
		response.Error(c, errorx.ErrNotFound.WithMsg("菜单不存在"))
		return
	}

	// 检查系统菜单
	if menu.IsSystem {
		response.Error(c, errorx.ErrForbidden.WithMsg("系统菜单不允许修改"))
		return
	}

	// 检查菜单名是否已被其他菜单使用
	if req.Name != menu.Name {
		existingMenu, err := h.menuRepo.List(ctx, 1, 1, &model.QueryOptions{
			Conditions: map[string]any{
				"name": req.Name,
			},
		})
		if err != nil {
			logger.ErrorContext(ctx, "检查菜单名失败", "error", err)
			response.Error(c, errorx.ErrInternal.WithMsg("检查菜单名失败"))
			return
		}
		if len(existingMenu) > 0 && existingMenu[0].ID != menuID {
			response.Error(c, errorx.ErrInvalidParams.WithMsg("菜单名已存在"))
			return
		}
	}

	// 检查父菜单（不能设置自己为父菜单，也不能形成循环）
	if req.ParentID > 0 {
		if req.ParentID == menuID {
			response.Error(c, errorx.ErrInvalidParams.WithMsg("不能设置自己为父菜单"))
			return
		}

		// 检查父菜单是否存在
		_, err := h.menuRepo.Get(ctx, req.ParentID, nil)
		if err != nil {
			response.Error(c, errorx.ErrInvalidParams.WithMsg("父菜单不存在"))
			return
		}
	}

	// 更新菜单信息
	menu.Name = req.Name
	menu.Title = req.Title
	menu.Icon = req.Icon
	menu.Path = req.Path
	menu.Component = req.Component
	menu.ParentID = req.ParentID
	menu.Type = req.Type
	menu.Permission = req.Permission
	menu.Redirect = req.Redirect
	menu.Meta = req.Meta
	menu.UpdatedBy = middleware.GetUserIDInt64(c)

	if req.Sort != nil {
		menu.Sort = *req.Sort
	}
	if req.Visible != nil {
		menu.Visible = *req.Visible
	}
	if req.Enabled != nil {
		menu.Enabled = *req.Enabled
	}
	if req.KeepAlive != nil {
		menu.KeepAlive = *req.KeepAlive
	}
	if req.External != nil {
		menu.External = *req.External
	}

	err = h.menuRepo.Update(ctx, menu)
	if err != nil {
		logger.ErrorContext(ctx, "更新菜单失败", "menu_id", menuID, "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("更新菜单失败"))
		return
	}

	// 转换为响应格式
	menuResp := &dto.MenuResponse{
		ID:         menu.ID,
		Name:       menu.Name,
		Title:      menu.Title,
		Icon:       menu.Icon,
		Path:       menu.Path,
		Component:  menu.Component,
		ParentID:   menu.ParentID,
		Type:       menu.Type,
		Permission: menu.Permission,
		Sort:       menu.Sort,
		Visible:    menu.Visible,
		Enabled:    menu.Enabled,
		IsSystem:   menu.IsSystem,
		KeepAlive:  menu.KeepAlive,
		External:   menu.External,
		Redirect:   menu.Redirect,
		Meta:       menu.Meta,
		CreatedBy:  menu.CreatedBy,
		UpdatedBy:  menu.UpdatedBy,
		CreatedAt:  menu.CreatedAt,
		UpdatedAt:  menu.UpdatedAt,
	}

	logger.InfoContext(ctx, "更新菜单成功", "menu_id", menuID, "menu_name", menu.Name)
	response.Success(c, menuResp)
}

// DeleteMenu 删除菜单
// @Summary 删除菜单
// @Description 删除菜单
// @Tags 菜单管理
// @Produce json
// @Param id path int true "菜单ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "菜单不存在"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/admin/menus/{id} [delete]
func (h *MenuHandler) DeleteMenu(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取菜单ID
	menuID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.Error(c, errorx.ErrInvalidParams.WithMsg("无效的菜单ID"))
		return
	}

	// 获取菜单信息
	menu, err := h.menuRepo.Get(ctx, menuID, nil)
	if err != nil {
		logger.ErrorContext(ctx, "获取菜单失败", "menu_id", menuID, "error", err)
		response.Error(c, errorx.ErrNotFound.WithMsg("菜单不存在"))
		return
	}

	// 检查系统菜单
	if menu.IsSystem {
		response.Error(c, errorx.ErrForbidden.WithMsg("系统菜单不允许删除"))
		return
	}

	// 检查是否有子菜单
	children, err := h.menuRepo.List(ctx, 1, 1, &model.QueryOptions{
		Conditions: map[string]any{
			"parent_id": menuID,
		},
	})
	if err != nil {
		logger.ErrorContext(ctx, "检查子菜单失败", "menu_id", menuID, "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("检查子菜单失败"))
		return
	}
	if len(children) > 0 {
		response.Error(c, errorx.ErrInvalidParams.WithMsg("存在子菜单，无法删除"))
		return
	}

	// 删除菜单
	err = h.menuRepo.Delete(ctx, menuID)
	if err != nil {
		logger.ErrorContext(ctx, "删除菜单失败", "menu_id", menuID, "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("删除菜单失败"))
		return
	}

	logger.InfoContext(ctx, "删除菜单成功", "menu_id", menuID, "menu_name", menu.Name)
	response.Success[any](c, nil)
}

// GetUserMenus 获取用户菜单
// @Summary 获取用户菜单
// @Description 获取当前用户可访问的菜单树
// @Tags 菜单管理
// @Produce json
// @Success 200 {object} response.Response{data=[]dto.UserMenuResponse} "获取成功"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/user/menus [get]
func (h *MenuHandler) GetUserMenus(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取用户ID
	userID := middleware.GetUserIDInt64(c)
	if userID == 0 {
		response.Error(c, errorx.ErrUserNoLogin)
		return
	}

	// 获取用户菜单
	menus, err := h.permissionService.GetUserMenus(ctx, userID)
	if err != nil {
		logger.ErrorContext(ctx, "获取用户菜单失败", "user_id", userID, "error", err)
		response.Error(c, errorx.ErrInternal.WithMsg("获取用户菜单失败"))
		return
	}

	// 转换为用户菜单响应格式
	userMenus := h.convertToUserMenus(menus)

	response.Success(c, userMenus)
}

// convertToUserMenus 转换为用户菜单格式
func (h *MenuHandler) convertToUserMenus(menus []model.Menu) []dto.UserMenuResponse {
	var result []dto.UserMenuResponse

	for _, menu := range menus {
		var meta map[string]any
		if menu.Meta != "" {
			if err := json.Unmarshal([]byte(menu.Meta), &meta); err != nil {
				// 如果解析失败，使用空的meta
				meta = make(map[string]any)
			}
		}

		userMenu := dto.UserMenuResponse{
			ID:        menu.ID,
			Name:      menu.Name,
			Title:     menu.Title,
			Icon:      menu.Icon,
			Path:      menu.Path,
			Component: menu.Component,
			Type:      menu.Type,
			Sort:      menu.Sort,
			KeepAlive: menu.KeepAlive,
			External:  menu.External,
			Redirect:  menu.Redirect,
			Meta:      meta,
			Children:  h.convertToUserMenus(menu.Children),
		}

		result = append(result, userMenu)
	}

	return result
}
