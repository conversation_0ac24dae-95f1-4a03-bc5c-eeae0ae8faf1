package permission

import (
	"context"
	"fmt"
	"strconv"

	"github.com/limitcool/starter/internal/model"
	"github.com/limitcool/starter/internal/pkg/casbin"
	"github.com/limitcool/starter/internal/pkg/errorx"
	"github.com/limitcool/starter/internal/pkg/logger"
)

// Service 权限服务
type Service struct {
	casbinService    *casbin.CasbinService
	userRepo         *model.UserRepo
	roleRepo         *model.RoleRepo
	permissionRepo   *model.PermissionRepo
	menuRepo         *model.MenuRepo
}

// NewService 创建权限服务
func NewService(
	casbinService *casbin.CasbinService,
	userRepo *model.UserRepo,
	roleRepo *model.RoleRepo,
	permissionRepo *model.PermissionRepo,
	menuRepo *model.MenuRepo,
) *Service {
	return &Service{
		casbinService:  casbinService,
		userRepo:       userRepo,
		roleRepo:       roleRepo,
		permissionRepo: permissionRepo,
		menuRepo:       menuRepo,
	}
}

// CheckPermission 检查用户权限
func (s *Service) CheckPermission(ctx context.Context, userID int64, resource, action string) (bool, error) {
	if s.casbinService == nil {
		// 如果Casbin未启用，默认允许所有操作
		return true, nil
	}

	userKey := casbin.GetUserKey(userID)
	resourceKey := casbin.GetResourceKey(resource)

	return s.casbinService.Enforce(ctx, userKey, resourceKey, action)
}

// AssignRolesToUser 为用户分配角色
func (s *Service) AssignRolesToUser(ctx context.Context, userID int64, roleIDs []int64) error {
	// 更新数据库中的用户角色关联
	err := s.userRepo.AssignRoles(ctx, userID, roleIDs)
	if err != nil {
		return errorx.WrapError(err, "分配用户角色失败")
	}

	if s.casbinService == nil {
		return nil
	}

	// 更新Casbin中的用户角色关联
	userKey := casbin.GetUserKey(userID)

	// 先删除用户的所有角色
	err = s.casbinService.DeleteRolesForUser(ctx, userKey)
	if err != nil {
		return errorx.WrapError(err, "清除用户角色失败")
	}

	// 添加新角色
	for _, roleID := range roleIDs {
		roleKey := casbin.GetRoleKey(roleID)
		err = s.casbinService.AddRoleForUser(ctx, userKey, roleKey)
		if err != nil {
			return errorx.WrapError(err, "添加用户角色失败")
		}
	}

	return nil
}

// AssignPermissionsToRole 为角色分配权限
func (s *Service) AssignPermissionsToRole(ctx context.Context, roleID int64, permissionIDs []int64) error {
	// 更新数据库中的角色权限关联
	err := s.roleRepo.AssignPermissions(ctx, roleID, permissionIDs)
	if err != nil {
		return errorx.WrapError(err, "分配角色权限失败")
	}

	if s.casbinService == nil {
		return nil
	}

	// 更新Casbin中的角色权限
	roleKey := casbin.GetRoleKey(roleID)

	// 获取权限详情
	permissions, err := s.permissionRepo.List(ctx, 1, 1000, &model.QueryOptions{
		Condition: "id IN (?)",
		Args:      []any{permissionIDs},
	})
	if err != nil {
		return errorx.WrapError(err, "查询权限详情失败")
	}

	// 先删除角色的所有权限策略
	// TODO: 需要实现删除角色所有权限的方法

	// 添加新权限策略
	for _, permission := range permissions {
		resourceKey := casbin.GetResourceKey(permission.Resource)
		err = s.casbinService.AddPolicy(ctx, roleKey, resourceKey, permission.Action)
		if err != nil {
			return errorx.WrapError(err, "添加角色权限策略失败")
		}
	}

	return nil
}

// GetUserPermissions 获取用户权限列表
func (s *Service) GetUserPermissions(ctx context.Context, userID int64) ([]model.Permission, error) {
	return s.permissionRepo.GetPermissionsByUserID(ctx, userID)
}

// GetUserRoles 获取用户角色列表
func (s *Service) GetUserRoles(ctx context.Context, userID int64) ([]model.Role, error) {
	return s.roleRepo.GetRolesByUserID(ctx, userID)
}

// GetUserMenus 获取用户可访问的菜单
func (s *Service) GetUserMenus(ctx context.Context, userID int64) ([]model.Menu, error) {
	return s.menuRepo.GetUserMenuTree(ctx, userID)
}

// SyncUserPermissions 同步用户权限到Casbin
func (s *Service) SyncUserPermissions(ctx context.Context, userID int64) error {
	if s.casbinService == nil {
		return nil
	}

	userKey := casbin.GetUserKey(userID)

	// 获取用户角色
	roles, err := s.roleRepo.GetRolesByUserID(ctx, userID)
	if err != nil {
		return errorx.WrapError(err, "获取用户角色失败")
	}

	// 清除用户现有角色
	err = s.casbinService.DeleteRolesForUser(ctx, userKey)
	if err != nil {
		return errorx.WrapError(err, "清除用户角色失败")
	}

	// 添加用户角色
	for _, role := range roles {
		roleKey := casbin.GetRoleKey(role.ID)
		err = s.casbinService.AddRoleForUser(ctx, userKey, roleKey)
		if err != nil {
			return errorx.WrapError(err, "添加用户角色失败")
		}
	}

	logger.InfoContext(ctx, "同步用户权限成功", "user_id", userID, "roles_count", len(roles))
	return nil
}

// SyncRolePermissions 同步角色权限到Casbin
func (s *Service) SyncRolePermissions(ctx context.Context, roleID int64) error {
	if s.casbinService == nil {
		return nil
	}

	roleKey := casbin.GetRoleKey(roleID)

	// 获取角色权限
	permissions, err := s.permissionRepo.GetPermissionsByRoleID(ctx, roleID)
	if err != nil {
		return errorx.WrapError(err, "获取角色权限失败")
	}

	// TODO: 清除角色现有权限策略
	// 需要实现删除角色所有权限的方法

	// 添加角色权限策略
	for _, permission := range permissions {
		resourceKey := casbin.GetResourceKey(permission.Resource)
		err = s.casbinService.AddPolicy(ctx, roleKey, resourceKey, permission.Action)
		if err != nil {
			return errorx.WrapError(err, "添加角色权限策略失败")
		}
	}

	logger.InfoContext(ctx, "同步角色权限成功", "role_id", roleID, "permissions_count", len(permissions))
	return nil
}

// InitializePermissions 初始化权限系统
func (s *Service) InitializePermissions(ctx context.Context) error {
	if s.casbinService == nil {
		logger.InfoContext(ctx, "Casbin未启用，跳过权限初始化")
		return nil
	}

	// 同步所有角色权限
	roles, err := s.roleRepo.GetEnabledRoles(ctx)
	if err != nil {
		return errorx.WrapError(err, "获取角色列表失败")
	}

	for _, role := range roles {
		err = s.SyncRolePermissions(ctx, role.ID)
		if err != nil {
			logger.ErrorContext(ctx, "同步角色权限失败", "role_id", role.ID, "error", err)
			continue
		}
	}

	// 同步所有用户角色
	// 这里可以根据需要实现批量同步用户权限

	logger.InfoContext(ctx, "权限系统初始化完成", "roles_count", len(roles))
	return nil
}

// HasPermission 检查用户是否有特定权限
func (s *Service) HasPermission(ctx context.Context, userID int64, resource, action string) (bool, error) {
	return s.CheckPermission(ctx, userID, resource, action)
}

// IsAdmin 检查用户是否是管理员
func (s *Service) IsAdmin(ctx context.Context, userID int64) (bool, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return false, errorx.WrapError(err, "获取用户信息失败")
	}
	return user.IsAdmin, nil
}

// GetUserInfo 获取用户权限信息
func (s *Service) GetUserInfo(ctx context.Context, userID int64) (*UserPermissionInfo, error) {
	// 获取用户基本信息
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, errorx.WrapError(err, "获取用户信息失败")
	}

	// 获取用户角色
	roles, err := s.GetUserRoles(ctx, userID)
	if err != nil {
		return nil, errorx.WrapError(err, "获取用户角色失败")
	}

	// 获取用户权限
	permissions, err := s.GetUserPermissions(ctx, userID)
	if err != nil {
		return nil, errorx.WrapError(err, "获取用户权限失败")
	}

	// 获取用户菜单
	menus, err := s.GetUserMenus(ctx, userID)
	if err != nil {
		return nil, errorx.WrapError(err, "获取用户菜单失败")
	}

	return &UserPermissionInfo{
		UserID:      userID,
		Username:    user.Username,
		IsAdmin:     user.IsAdmin,
		Roles:       roles,
		Permissions: permissions,
		Menus:       menus,
	}, nil
}

// UserPermissionInfo 用户权限信息
type UserPermissionInfo struct {
	UserID      int64              `json:"user_id"`
	Username    string             `json:"username"`
	IsAdmin     bool               `json:"is_admin"`
	Roles       []model.Role       `json:"roles"`
	Permissions []model.Permission `json:"permissions"`
	Menus       []model.Menu       `json:"menus"`
}
