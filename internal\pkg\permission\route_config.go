package permission

import (
	"context"
	"fmt"
	"strings"

	"github.com/limitcool/starter/internal/pkg/logger"
)

// RoutePermission 路由权限配置
type RoutePermission struct {
	Method     string   `json:"method"`      // HTTP方法
	Path       string   `json:"path"`        // 路径模式
	Resource   string   `json:"resource"`    // 资源标识
	Action     string   `json:"action"`      // 操作类型
	Roles      []string `json:"roles"`       // 所需角色
	AdminOnly  bool     `json:"admin_only"`  // 仅管理员
	Public     bool     `json:"public"`      // 公开访问
	Disabled   bool     `json:"disabled"`    // 是否禁用
}

// RouteConfig 路由权限配置管理器
type RouteConfig struct {
	routes map[string]*RoutePermission // key: method:path
}

// NewRouteConfig 创建路由权限配置管理器
func NewRouteConfig() *RouteConfig {
	return &RouteConfig{
		routes: make(map[string]*RoutePermission),
	}
}

// AddRoute 添加路由权限配置
func (rc *RouteConfig) AddRoute(method, path, resource, action string, options ...RouteOption) {
	route := &RoutePermission{
		Method:   strings.ToUpper(method),
		Path:     path,
		Resource: resource,
		Action:   action,
	}

	// 应用选项
	for _, option := range options {
		option(route)
	}

	key := rc.buildKey(method, path)
	rc.routes[key] = route

	logger.DebugContext(context.Background(), "添加路由权限配置",
		"method", method,
		"path", path,
		"resource", resource,
		"action", action,
		"admin_only", route.AdminOnly,
		"public", route.Public,
		"roles", route.Roles)
}

// GetRoute 获取路由权限配置
func (rc *RouteConfig) GetRoute(method, path string) *RoutePermission {
	key := rc.buildKey(method, path)
	return rc.routes[key]
}

// IsPublicRoute 检查是否是公开路由
func (rc *RouteConfig) IsPublicRoute(method, path string) bool {
	route := rc.GetRoute(method, path)
	return route != nil && route.Public
}

// IsAdminOnlyRoute 检查是否是仅管理员路由
func (rc *RouteConfig) IsAdminOnlyRoute(method, path string) bool {
	route := rc.GetRoute(method, path)
	return route != nil && route.AdminOnly
}

// GetRequiredRoles 获取路由所需角色
func (rc *RouteConfig) GetRequiredRoles(method, path string) []string {
	route := rc.GetRoute(method, path)
	if route != nil {
		return route.Roles
	}
	return nil
}

// GetResourceAction 获取路由的资源和操作
func (rc *RouteConfig) GetResourceAction(method, path string) (string, string) {
	route := rc.GetRoute(method, path)
	if route != nil {
		return route.Resource, route.Action
	}
	return "", ""
}

// buildKey 构建路由键
func (rc *RouteConfig) buildKey(method, path string) string {
	return fmt.Sprintf("%s:%s", strings.ToUpper(method), path)
}

// ListRoutes 列出所有路由配置
func (rc *RouteConfig) ListRoutes() map[string]*RoutePermission {
	result := make(map[string]*RoutePermission)
	for key, route := range rc.routes {
		result[key] = route
	}
	return result
}

// RouteOption 路由选项
type RouteOption func(*RoutePermission)

// WithRoles 设置所需角色
func WithRoles(roles ...string) RouteOption {
	return func(route *RoutePermission) {
		route.Roles = roles
	}
}

// WithAdminOnly 设置仅管理员访问
func WithAdminOnly() RouteOption {
	return func(route *RoutePermission) {
		route.AdminOnly = true
	}
}

// WithPublic 设置公开访问
func WithPublic() RouteOption {
	return func(route *RoutePermission) {
		route.Public = true
	}
}

// WithDisabled 设置禁用
func WithDisabled() RouteOption {
	return func(route *RoutePermission) {
		route.Disabled = true
	}
}

// InitDefaultRoutes 初始化默认路由权限配置
func (rc *RouteConfig) InitDefaultRoutes() {
	// 公开路由
	publicRoutes := []struct {
		method, path string
	}{
		{"GET", "/api/v1/health"},
		{"GET", "/api/v1/version"},
		{"POST", "/api/v1/auth/login"},
		{"POST", "/api/v1/auth/register"},
		{"POST", "/api/v1/auth/refresh"},
		{"POST", "/api/v1/auth/logout"},
		{"GET", "/swagger/*"},
		{"GET", "/docs/*"},
	}

	for _, route := range publicRoutes {
		rc.AddRoute(route.method, route.path, "public", "access", WithPublic())
	}

	// 用户路由
	userRoutes := []struct {
		method, path, resource, action string
	}{
		{"GET", "/api/v1/user/profile", "user", "read"},
		{"PUT", "/api/v1/user/profile", "user", "update"},
		{"POST", "/api/v1/user/avatar", "user", "update"},
		{"GET", "/api/v1/user/menus", "menu", "read"},
	}

	for _, route := range userRoutes {
		rc.AddRoute(route.method, route.path, route.resource, route.action)
	}

	// 管理员路由
	adminRoutes := []struct {
		method, path, resource, action string
	}{
		// 用户管理
		{"GET", "/api/v1/admin/users", "user", "read"},
		{"POST", "/api/v1/admin/users", "user", "create"},
		{"GET", "/api/v1/admin/users/:id", "user", "read"},
		{"PUT", "/api/v1/admin/users/:id", "user", "update"},
		{"DELETE", "/api/v1/admin/users/:id", "user", "delete"},
		{"POST", "/api/v1/admin/users/:id/roles", "user", "update"},

		// 角色管理
		{"GET", "/api/v1/admin/roles", "role", "read"},
		{"POST", "/api/v1/admin/roles", "role", "create"},
		{"GET", "/api/v1/admin/roles/:id", "role", "read"},
		{"PUT", "/api/v1/admin/roles/:id", "role", "update"},
		{"DELETE", "/api/v1/admin/roles/:id", "role", "delete"},
		{"POST", "/api/v1/admin/roles/:id/permissions", "role", "update"},

		// 权限管理
		{"GET", "/api/v1/admin/permissions", "permission", "read"},
		{"POST", "/api/v1/admin/permissions", "permission", "create"},
		{"GET", "/api/v1/admin/permissions/:id", "permission", "read"},
		{"PUT", "/api/v1/admin/permissions/:id", "permission", "update"},
		{"DELETE", "/api/v1/admin/permissions/:id", "permission", "delete"},
		{"GET", "/api/v1/admin/permissions/categories", "permission", "read"},
		{"GET", "/api/v1/admin/permissions/options", "permission", "read"},

		// 菜单管理
		{"GET", "/api/v1/admin/menus", "menu", "read"},
		{"POST", "/api/v1/admin/menus", "menu", "create"},
		{"GET", "/api/v1/admin/menus/:id", "menu", "read"},
		{"PUT", "/api/v1/admin/menus/:id", "menu", "update"},
		{"DELETE", "/api/v1/admin/menus/:id", "menu", "delete"},
		{"GET", "/api/v1/admin/menus/tree", "menu", "read"},
		{"POST", "/api/v1/admin/menus/:id/permissions", "menu", "update"},
	}

	for _, route := range adminRoutes {
		rc.AddRoute(route.method, route.path, route.resource, route.action, WithAdminOnly())
	}

	logger.InfoContext(context.Background(), "初始化默认路由权限配置完成",
		"public_routes", len(publicRoutes),
		"user_routes", len(userRoutes),
		"admin_routes", len(adminRoutes))
}

// MatchPath 匹配路径模式
func (rc *RouteConfig) MatchPath(pattern, path string) bool {
	// 简单的路径匹配，支持 :id 参数
	patternParts := strings.Split(pattern, "/")
	pathParts := strings.Split(path, "/")

	if len(patternParts) != len(pathParts) {
		return false
	}

	for i, patternPart := range patternParts {
		if patternPart == "" {
			continue
		}
		
		// 如果是参数（以:开头），跳过检查
		if strings.HasPrefix(patternPart, ":") {
			continue
		}
		
		// 如果是通配符（以*结尾），匹配剩余部分
		if strings.HasSuffix(patternPart, "*") {
			return true
		}
		
		// 精确匹配
		if patternPart != pathParts[i] {
			return false
		}
	}

	return true
}

// FindMatchingRoute 查找匹配的路由配置
func (rc *RouteConfig) FindMatchingRoute(method, path string) *RoutePermission {
	// 首先尝试精确匹配
	if route := rc.GetRoute(method, path); route != nil {
		return route
	}

	// 然后尝试模式匹配
	for key, route := range rc.routes {
		parts := strings.SplitN(key, ":", 2)
		if len(parts) == 2 && parts[0] == strings.ToUpper(method) {
			if rc.MatchPath(parts[1], path) {
				return route
			}
		}
	}

	return nil
}
